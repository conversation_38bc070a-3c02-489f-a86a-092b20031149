{% extends "base.html" %}

{% load i18n %}

{% block head_title %}{% trans "Home" %}{% endblock %}

{% block content %}
{% include 'components/landing-header.html' %}
<div class="max-w-2xl mx-auto">
  <div class="my-card">
    <div class="mb-6 text-center">
      <h1 class="my-h1">{% trans "Welcome to QWIZIK!" %}</h1>
    </div>
    
    {% if user.is_authenticated %}
      <div id="login-success" class="my-card mb-6 flex flex-col items-start gap-2">
        <h2 class="my-h2 mb-2">{% trans "You are logged in!" %}</h2>
        <p class="my-p">{% trans "Welcome back, " %}<span>{{ user.first_name }}!</span> </p>
        <p>{% trans "You have successfully authenticated with your account. You can now access all the features of the application. " %}</p>
      </div>

      <div class="mt-6">
        <a href="{% url 'account_logout' %}" class="btn btn-error w-full">{% trans "Sign Out" %}</a>
      </div>
    {% else %}
      <div class="my-card mb-6">
        <h2 class="my-h2 mb-2">{% trans "You are not logged in" %}</h2>
        <p class="my-p">{% trans "Please sign in to access all features of the application." %}</p>
      </div>
      
      <div class="mt-6 space-y-3">
        <a href="{% url 'account_login' %}" class="btn btn-primary w-full">{% trans "Sign In" %}</a>
        <a href="{% url 'account_signup' %}" class="btn btn-outline btn-primary w-full">{% trans "Sign Up" %}</a>
      </div>
    {% endif %}
  </div>
</div>
{% endblock %}