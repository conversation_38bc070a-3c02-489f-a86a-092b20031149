<!-- <PERSON> Button Question -->
<div class="space-y-3">
    {% for choice in question.choices.all %}
    <label class="flex items-center space-x-3 cursor-pointer hover:bg-base-200 p-3 rounded-lg transition-colors">
        <input type="radio" 
               name="answer" 
               value="{{ choice.id }}"
               class="radio radio-primary"
               {% if existing_answer == choice.id %}checked{% endif %}
               {% if question.is_required %}required{% endif %}>
        <span class="my-p flex-1">{{ choice.content }}</span>
    </label>
    {% endfor %}
</div>

{% if question.is_required %}
<p class="my-p text-base-content/60 mt-3 text-sm">
    <span class="text-error">*</span> This question is required
</p>
{% endif %}
