<!-- Question Content -->
<div class="my-card">
    <div class="mb-6">
        <h3 class="my-h3 mb-2">
            Question {{ progress.current }} of {{ progress.total }}
            {% if question.is_required %}
                <span class="text-error">*</span>
            {% endif %}
        </h3>
        <p class="my-p">{{ question.content }}</p>
    </div>

    <!-- Question Form -->
    <form hx-post="{% url 'survey:survey_question' survey.slug %}"
          hx-target="#question-container"
          hx-swap="innerHTML"
          hx-indicator="#loading-indicator">
        {% csrf_token %}
        
        {% if question.question_type == 'RADIO' %}
            {% include 'survey/partials/radio_question.html' %}
        {% elif question.question_type == 'TEXT' %}
            {% include 'survey/partials/text_question.html' %}
        {% endif %}

        <!-- Submit Button -->
        <div class="mt-6 text-center">
            <button type="submit" class="btn btn-primary btn-lg">
                {% if progress.current == progress.total %}
                    Complete Survey
                {% else %}
                    Next Question
                {% endif %}
            </button>
        </div>
    </form>
</div>

<!-- Update Progress Bar -->
<script>
    // Update progress bar
    const progressBar = document.querySelector('.bg-primary');
    const progressText = document.querySelector('.text-xs .flex');
    if (progressBar) {
        progressBar.style.width = '{{ progress.percentage }}%';
    }
    if (progressText) {
        progressText.innerHTML = `
            <span>Question {{ progress.current }} of {{ progress.total }}</span>
            <span>{{ progress.percentage|floatformat:0 }}% Complete</span>
        `;
    }
</script>
