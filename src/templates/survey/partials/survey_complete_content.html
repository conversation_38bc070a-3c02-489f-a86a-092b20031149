<!-- Survey Completion -->
<div class="my-card text-center">
    <div class="mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-16 w-16 text-success mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h1 class="my-h1 text-success">Survey Completed!</h1>
        <p class="my-p text-base-content/70">Thank you for completing the {{ survey.title }} survey.</p>
    </div>

    <div class="space-y-4">
        <p class="my-p text-base-content/70">
            Your responses have been recorded and will help us improve our services.
        </p>
        
        <div class="flex justify-center gap-4">
            <a href="{% url 'survey:survey_list' %}" class="btn btn-outline">
                View Other Surveys
            </a>
            <a href="{% url 'home' %}" class="btn btn-primary">
                Return Home
            </a>
        </div>
    </div>
</div>

<!-- Update Progress Bar to 100% -->
<script>
    const progressBar = document.querySelector('.bg-primary');
    const progressText = document.querySelector('.text-xs .flex');
    if (progressBar) {
        progressBar.style.width = '100%';
    }
    if (progressText) {
        progressText.innerHTML = `
            <span>Survey Complete!</span>
            <span>100% Complete</span>
        `;
    }
</script>
