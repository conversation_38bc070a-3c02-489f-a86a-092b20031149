{% extends 'survey/base_survey.html' %}

{% block survey_content %}
<div class="max-w-4xl mx-auto">
    <!-- Survey Header -->
    <div class="my-card">
        <div class="text-center">
            <h1 class="my-h2">{{ survey.title }}</h1>
            <p class="my-p text-base-content/60">{{ survey.description }}</p>
        </div>
    </div>

    <!-- Survey Form -->
    <form method="post" class="space-y-6 mt-6">
        {% csrf_token %}
        
        {% for question in questions %}
        <div class="my-card">
            <div class="mb-4">
                <h3 class="my-h3 mb-2">
                    Question {{ forloop.counter }}
                    {% if question.is_required %}
                        <span class="text-error">*</span>
                    {% endif %}
                </h3>
                <p class="my-p">{{ question.content }}</p>
            </div>

            {% if question.question_type == 'RADIO' %}
                <!-- <PERSON> Button Questions -->
                <div class="space-y-3">
                    {% for choice in question.choices.all %}
                    <label class="flex items-center space-x-3 cursor-pointer hover:bg-base-200 p-2 rounded">
                        <input type="radio"
                               name="question_{{ question.id }}"
                               value="{{ choice.id }}"
                               class="radio radio-primary"
                               {% if question.existing_choice_id == choice.id %}checked{% endif %}>
                        <span class="my-p">{{ choice.content }}</span>
                    </label>
                    {% endfor %}
                </div>
            
            {% elif question.question_type == 'TEXT' %}
                <!-- Text Input Questions -->
                <div>
                    <textarea name="question_{{ question.id }}"
                              rows="4"
                              class="my-input"
                              placeholder="Enter your response here..."
                              {% if question.is_required %}required{% endif %}>{{ question.existing_answer }}</textarea>
                </div>
            {% endif %}
        </div>
        {% endfor %}

        <!-- Submit Button -->
        <div class="my-card text-center">
            <button type="submit" class="btn btn-primary btn-lg">
                Complete Survey
            </button>
            <p class="my-p text-base-content/60 mt-2">
                <span class="text-error">*</span> Required fields
            </p>
        </div>
    </form>
</div>

{% endblock %}
