{% extends 'base.html' %}

{% block content %}
<div class="min-h-screen bg-base-200 py-8">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <!-- Survey Header -->
            <div class="my-card">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h1 class="my-h2">{{ survey.title }}</h1>
                        <p class="my-p text-base-content/60">{{ survey.description }}</p>
                    </div>
                </div>
                
                <!-- Progress Bar -->
                <div class="w-full bg-base-300 rounded-full h-2 mb-2">
                    <div class="bg-primary h-2 rounded-full transition-all duration-300" 
                         style="width: {{ progress.percentage }}%"></div>
                </div>
                <div class="flex justify-between text-xs text-base-content/60">
                    <span>Question {{ progress.current }} of {{ progress.total }}</span>
                    <span>{{ progress.percentage|floatformat:0 }}% Complete</span>
                </div>
            </div>

            <!-- Question Container -->
            <div id="question-container" 
                 hx-get="{% url 'survey:survey_question' survey.slug %}"
                 hx-trigger="load"
                 hx-target="this" 
                 hx-swap="innerHTML"
                 hx-indicator="#loading-indicator">
            </div>

            <!-- Loading Indicator -->
            <div id="loading-indicator" class="htmx-indicator">
                <div class="my-card text-center">
                    <div class="loading loading-spinner loading-lg text-primary"></div>
                    <p class="my-p mt-2">Loading question...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
