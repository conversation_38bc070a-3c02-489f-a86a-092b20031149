{% extends 'survey/base_survey.html' %}

{% block survey_content %}
<div class="max-w-4xl mx-auto">
    <div class="my-card">
        <h1 class="my-h1">Available Surveys</h1>
        <p class="my-p text-base-content/60">Complete surveys to help us understand your needs better.</p>
    </div>

    {% if surveys %}
        <div class="grid gap-6 mt-6">
            {% for survey in surveys %}
            <div class="my-card hover:shadow-md transition-shadow">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <h2 class="my-h2 mb-2">{{ survey.title }}</h2>
                        <p class="my-p text-base-content/70 mb-4">{{ survey.description }}</p>
                        <div class="flex items-center gap-4 text-sm text-base-content/60">
                            <span>{{ survey.total_questions }} question{{ survey.total_questions|pluralize }}</span>
                            <span>•</span>
                            <span>Created {{ survey.created_at|date:"M d, Y" }}</span>
                        </div>
                    </div>
                    <div class="ml-4">
                        <a href="{% url 'survey:survey_detail' survey.slug %}" 
                           class="btn btn-primary">
                            Start Survey
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="my-card text-center mt-6">
            <div class="py-8">
                <h2 class="my-h2 mb-2">No Surveys Available</h2>
                <p class="my-p text-base-content/60">Check back later for new surveys.</p>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
