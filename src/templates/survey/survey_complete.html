{% extends 'survey/base_survey.html' %}

{% block survey_content %}
<div class="max-w-4xl mx-auto">
    <div class="my-card text-center">
        <div class="mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-16 w-16 text-success mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h1 class="my-h1 text-success">Survey Completed!</h1>
            <p class="my-p text-base-content/70">Thank you for completing the {{ survey.title }} survey.</p>
        </div>

        <div class="bg-base-200 rounded-lg p-6 mb-6">
            <h2 class="my-h2 mb-4">Survey Summary</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-primary">{{ survey.total_questions }}</div>
                    <div class="text-sm text-base-content/60">Questions Answered</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-primary">{{ response.started_at|date:"M d" }}</div>
                    <div class="text-sm text-base-content/60">Started</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-primary">{{ response.completed_at|date:"M d" }}</div>
                    <div class="text-sm text-base-content/60">Completed</div>
                </div>
            </div>
        </div>

        <div class="space-y-4">
            <p class="my-p text-base-content/70">
                Your responses have been recorded and will help us improve our services.
            </p>
            
            <div class="flex justify-center gap-4">
                <a href="{% url 'survey:survey_list' %}" class="btn btn-outline">
                    View Other Surveys
                </a>
                <a href="{% url 'home' %}" class="btn btn-primary">
                    Return Home
                </a>
            </div>
        </div>
    </div>

    <!-- Response Details (Optional) -->
    <div class="my-card mt-6">
        <h2 class="my-h2 mb-4">Your Responses</h2>
        <div class="space-y-4">
            {% for answer in response.answers.all %}
            <div class="border-l-4 border-primary pl-4">
                <h3 class="my-h3 mb-2">{{ answer.question.content }}</h3>
                {% if answer.selected_choice %}
                    <p class="my-p text-primary">{{ answer.selected_choice.content }}</p>
                {% elif answer.text_answer %}
                    <p class="my-p">{{ answer.text_answer }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
