{% extends 'survey/base_survey.html' %}

{% block survey_content %}
<div class="max-w-4xl mx-auto">
    <div class="my-card">
        <div class="text-center">
            <h1 class="my-h1 mb-4">{{ survey.title }}</h1>
            <p class="my-p text-base-content/70 mb-6">{{ survey.description }}</p>
            
            <div class="flex justify-center items-center gap-6 mb-6 text-sm text-base-content/60">
                <span>{{ survey.total_questions }} question{{ survey.total_questions|pluralize }}</span>
                <span>•</span>
                <span>Estimated time: {{ survey.total_questions|add:2 }} minutes</span>
            </div>

            {% if user.is_authenticated %}
                {% if user_response %}
                    {% if user_response.is_complete %}
                        <div class="alert alert-success mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>You have already completed this survey on {{ user_response.completed_at|date:"M d, Y" }}.</span>
                        </div>
                        <a href="{% url 'survey:survey_complete' survey.slug %}" class="btn btn-outline">
                            View Results
                        </a>
                    {% else %}
                        <div class="alert alert-info mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>You have started this survey. Continue where you left off.</span>
                        </div>
                        <a href="{% url 'survey:take_survey' survey.slug %}" class="btn btn-primary">
                            Continue Survey
                        </a>
                    {% endif %}
                {% else %}
                    <a href="{% url 'survey:take_survey' survey.slug %}" class="btn btn-primary btn-lg">
                        Start Survey
                    </a>
                {% endif %}
            {% else %}
                <div class="alert alert-warning mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <span>Please log in to take this survey.</span>
                </div>
                <a href="{% url 'account_login' %}" class="btn btn-primary">
                    Log In
                </a>
            {% endif %}
        </div>
    </div>

    <div class="my-card mt-6">
        <h2 class="my-h2 mb-4">Survey Information</h2>
        <div class="space-y-2 text-sm">
            <div class="flex justify-between">
                <span class="text-base-content/60">Total Questions:</span>
                <span>{{ survey.total_questions }}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-base-content/60">Created:</span>
                <span>{{ survey.created_at|date:"M d, Y" }}</span>
            </div>
            <div class="flex justify-between">
                <span class="text-base-content/60">Last Updated:</span>
                <span>{{ survey.updated_at|date:"M d, Y" }}</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}
