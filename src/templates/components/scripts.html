<!-- HTMX -->
<script src="https://unpkg.com/htmx.org@1.9.10"></script>

<!-- Alpine.js -->
<script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

<!-- Custom JavaScript -->
<script>
  // Initialize any custom JavaScript here
  document.addEventListener('DOMContentLoaded', function() {
    // HTMX configuration
    htmx.config.globalViewTransitions = true;

    // Custom HTMX event handlers
    document.body.addEventListener('htmx:beforeRequest', function(evt) {
      // Show loading state
      const indicator = document.querySelector('#loading-indicator');
      if (indicator) {
        indicator.style.display = 'block';
      }
    });

    document.body.addEventListener('htmx:afterRequest', function(evt) {
      // Hide loading state
      const indicator = document.querySelector('#loading-indicator');
      if (indicator) {
        indicator.style.display = 'none';
      }
    });
  });
</script>

{% block extra_scripts %}{% endblock %}