<!--TEMPLATE/ACCOUNTS/PASSWORD_RESET_FROM_KEY.HTML-->
{% extends 'base.html' %}
{% load static %}

{% block content %}
{% include 'components/landing-header.html' %}
<div class="container mx-auto md:max-w-[320px] lg:max-w-[480px] px-4 py-8">
<div x-data="{ showPassword: false, showPasswordConfirm: false }">
    {% if token_fail %}
        <div class="alert alert-error mb-6">
            <div>
                <i class="fa-regular fa-circle-exclamation"></i>
                <p class="font-poppins text-xs md:text-sm">The password reset link was invalid, possibly because it
                    has already been used or has expired.</p>
            </div>
        </div>
        <div class="text-center mt-6">
            <a href="{% url 'account:password_reset' %}" class="btn btn-primary">Request a new password reset link</a>
        </div>
    {% else %}
        {% if form %}
            <form method="post" action="{{ action_url }}">
                {% csrf_token %}
                {% if form.non_field_errors %}
                <div class="alert alert-error mb-4">
                    {% for error in form.non_field_errors %}
                        <div>{{ error }}</div>
                    {% endfor %}
                </div>
                {% endif %}
                <div class="form-control mb-4">
                    <label class="label" for="id_password1">
                        <span class="font-poppins text-xs md:text-sm">New Password</span>
                    </label>
                    <div class="relative">
                        <input :type="showPassword ? 'text' : 'password'" name="password1" id="id_password1"
                               placeholder="••••••••"
                               class="input w-full my-input pr-10 {% if form.password1.errors
                                       %}input-error{% endif %}"
                               required>
                        <button type="button" @click="showPassword = !showPassword"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                                tabindex="-1">
                            <i class="fa-regular" :class="showPassword ? 'fa-eye' : 'fa-eye-slash'"></i>
                        </button>
                    </div>
                    {% if form.password1.errors %}
                        <label class="label">
                            <span class="label-text-alt text-error">{{ form.password1.errors.0 }}</span>
                        </label>
                    {% endif %}
                </div>

                <div class="form-control mb-6">
                    <label class="label" for="id_password2">
                        <span class="font-poppins text-xs md:text-sm">Confirm New Password</span>
                    </label>
                    <div class="relative">
                        <input :type="showPasswordConfirm ? 'text' : 'password'" name="password2" id="id_password2"
                               placeholder="••••••••"
                               class="input w-full my-input pr-10 {% if form.password2.errors %}input-error{% endif %}"
                               required>
                        <button type="button" @click="showPasswordConfirm = !showPasswordConfirm"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                                tabindex="-1">
                            <i class="fa-regular" :class="showPasswordConfirm ? 'fa-eye' : 'fa-eye-slash'"></i>
                        </button>
                    </div>
                  {% if form.password2.errors %}
                        <label class="label">
                            <span class="label-text-alt text-error">{{ form.password2.errors.0 }}</span>
                        </label>
                   {% endif %}
                </div>
                <div class="form-control mb-4">
                    <button type="submit" class="btn btn-primary w-full">Change Password</button>
                </div>
            </form>
        {% endif %}
        {% if not form %}
            <div class="alert alert-success mb-6">
                <div>
                    <i class="fa-regular fa-check-circle"></i>
                    <p class="font-poppins text-xs md:text-sm">Your password has been changed successfully!</p>
                </div>
            </div>
            <div class="text-center mt-6">
                <a href="{% url 'account_login' %}" class="btn btn-primary">
                    Sign In with New Password</a>
            </div>
        {% endif %}
</div>
</div>
{% endblock %}
