{% extends 'account/base_accounts.html' %}
{% load static %}
{% block account_title %}Update Email Addresses{% endblock %}
{% block account_content %}

<div x-data="{ showAddEmailForm: false }">

    {% if user.emailaddress_set.all %}
    <p class="font-poppins text-xs md:text-sm">The following email addresses are associated with your account:</p>

    <div class="overflow-x-auto mb-6">
        <table class="table table-zebra w-full">
            <thead>
                <tr>
                    <th>Email</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for emailaddress in user.emailaddress_set.all %}
                <tr>
                    <!-- Email + badges -->
                    <td class="align-top">
                        <div class="font-medium mb-1 break-all">{{ emailaddress.email }}</div>

                        <div class="flex flex-wrap gap-2 mt-1">
                            {% if emailaddress.verified %}
                                <div class="badge badge-success text-xs whitespace-nowrap">Verified</div>
                            {% else %}
                                <div class="badge badge-warning text-xs whitespace-nowrap">Unverified</div>
                            {% endif %}

                            {% if emailaddress.primary %}
                                <div class="badge badge-primary text-xs whitespace-nowrap">Primary</div>
                            {% endif %}
                        </div>
                    </td>

                    <!-- Actions: styled to match badges -->
                    <td class="flex flex-wrap gap-2 pt-2">
                        {% if not emailaddress.primary %}
                        <!-- Make Primary -->
                        <form method="post" action="{% url 'account_email' %}">
                            {% csrf_token %}
                            <input type="hidden" name="email" value="{{ emailaddress.email }}">
                            <input type="hidden" name="action_primary" value="1">
                            {% if emailaddress.verified %}
                            <button type="submit"
                                    class="cursor-pointer bg-primary text-white text-xs px-2 py-1 rounded border">
                                Make Primary
                            </button>
                            {% endif %}
                        </form>
                        {% endif %}

                        {% if not emailaddress.verified %}
                        <!-- Resend Verification -->
                        <form method="post" action="{% url 'account_email' %}">
                            {% csrf_token %}
                            <input type="hidden" name="email" value="{{ emailaddress.email }}">
                            <input type="hidden" name="action_send" value="1">
                            <button type="submit"
                                    class="cursor-pointer text-xs px-2 py-1 border border-black text-black rounded hover:bg-gray-100"
                                    title="Resend Verification Email">
                                Resend
                            </button>
                        </form>
                        {% endif %}

                        <!-- Actions -->
                        <td class="relative min-h-[48px]">

                            <!-- Remove -->
                            <form method="post" action="{% url 'account_email' %}"
                                  class="absolute bottom-1 right-1">
                                {% csrf_token %}
                                <input type="hidden" name="email" value="{{ emailaddress.email }}">
                                <input type="hidden" name="action_remove" value="1">
                                {% if not emailaddress.primary %}
                                <button type="submit"
                                        class="w-6 h-6 flex items-center justify-center rounded hover:bg-red-100 group"
                                        title="Remove Email">
                                    <i class="cursor-pointer fa-regular fa-trash-can text-error text-sm
                                    group-hover:text-red-700"></i>
                                </button>
                                {% endif %}
                            </form>

                        </td>

                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="alert alert-warning mb-6">
        <div>
            <i class="fa-regular fa-exclamation-triangle"></i>
            <span>You currently do not have any email address set up. You should add an email address so you can receive notifications and reset your password.</span>
        </div>
    </div>
    {% endif %}

    <div class="divider">Add Email Address</div>

    <div>
        <button @click="showAddEmailForm = !showAddEmailForm" class="btn btn-outline btn-primary mb-4">
            <i class="fa-regular" :class="showAddEmailForm ? 'fa-minus' : 'fa-plus'"></i>
            <span x-text="showAddEmailForm ? 'Cancel' : 'Add Email'"></span>
        </button>

        <div x-show="showAddEmailForm" x-transition>
            <form method="post" action="{% url 'account_email' %}">
                {% csrf_token %}
                <input type="hidden" name="action_add" value="1" />

                <div class="form-control mb-4">
                    <label class="label" for="id_email">
                        <span class="label-text">Email</span>
                    </label>
                    <input type="email" name="email" id="id_email"
                           placeholder="<EMAIL>"
                           class="input w-full my-input"
                           required>
                </div>

                <div class="form-control">
                    <button type="submit" class="btn btn-primary">Add Email</button>
                </div>
            </form>
        </div>
    </div>

    <div class="mt-6 text-center">
        <a href="{% url 'account:profile' %}" class="btn btn-outline">
            Profile
        </a>
    </div>
</div>
{% endblock %}
