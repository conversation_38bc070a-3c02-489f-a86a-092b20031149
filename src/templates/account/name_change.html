<!--TEMPLATE/ACCOUNTS/NAME_CHANGE.HTML-->
{% extends 'account/base_accounts.html' %}
{% load static %}

{% block account_title %}Update Name{% endblock %}

{% block account_content %}
<div>
    <form method="post" action="{% url 'account:name_change' %}">
        {% csrf_token %}

        {% if form.non_field_errors %}
        <div class="alert alert-error mb-4">
            {% for error in form.non_field_errors %}
                <div>{{ error }}</div>
            {% endfor %}
        </div>
        {% endif %}

        <div class="form-control mb-4">
            <label class="label" for="id_first_name">
                <p class="font-poppins text-xs md:text-sm">First Name</p>
            </label>
            <input type="text" name="first_name" id="id_first_name"
                   value="{{ user.first_name }}"
                   class="input w-full outline-content border-1 border-neutral-content rounded-sm p-6 outline-none
               focus:outline-none focus:ring-0 focus:border-neutral-content {% if form.first_name.errors %}input-error{% endif %}"
                   required>
            {% if form.first_name.errors %}
                <label class="label">
                    <span class="label-text-alt text-error">{{ form.first_name.errors.0 }}</span>
                </label>
            {% endif %}
        </div>

        <div class="form-control mb-6">
            <label class="label" for="id_last_name">
                <p class="font-poppins text-xs md:text-sm">Last Name</p>
            </label>
            <input type="text" name="last_name" id="id_last_name"
                   value="{{ user.last_name }}"
                   class="input w-full outline-content border-1 border-neutral-content rounded-sm p-6 outline-none
               focus:outline-none focus:ring-0 focus:border-neutral-content {% if form.last_name.errors %}input-error{% endif %}"
                   required>
            {% if form.last_name.errors %}
                <label class="label">
                    <span class="label-text-alt text-error">{{ form.last_name.errors.0 }}</span>
                </label>
            {% endif %}
        </div>

        <div class="flex justify-between">
            <a href="{% url 'account:profile' %}" class="btn btn-outline">
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">Update Name</button>
        </div>
    </form>
</div>
{% endblock %}