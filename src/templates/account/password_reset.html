<!--TEMPLATE/ACCOUNTS/PASSWORD_RESET.HTML-->
{% extends 'base.html' %}
{% load static %}
{% block content %}
{% include 'components/landing-header.html' %}
<div class="container mx-auto md:max-w-[320px] lg:max-w-[480px] space-y-4 md:space-y-6">

    <form method="post" action="{% url 'account:password_reset' %}">
        {% csrf_token %}

        {% if form.non_field_errors %}
        <div class="alert alert-error mb-4">
            {% for error in form.non_field_errors %}
                <div>{{ error }}</div>
            {% endfor %}
        </div>
        {% endif %}

        <p class="font-poppins text-xs md:text-sm mb-4 md:mb-6">
            Forgotten your password? Enter your email address below, and we'll send you an email allowing you to reset it.
        </p>

        <div class="form-control mb-6">
            <label class="label" for="id_email">
                <span class="font-poppins text-xs md:text-sm">Email</span>
            </label>
            <input type="email" name="email" id="id_email" placeholder="<EMAIL>"
                   class="input w-full my-input {% if form.email.errors %}input-error{% endif %}"
                   required>
            {% if form.email.errors %}
                <label class="label">
                    <span class="label-text-alt text-error">{{ form.email.errors.0 }}</span>
                </label>
            {% endif %}
        </div>

        <div class="form-control mb-4">
            <button type="submit" class="btn btn-primary w-full">Reset My Password</button>
        </div>

        <div class="text-center font-poppins text-xs md:text-sm">
            <a href="{% url 'account:login' %}" class="text-primary cursor-pointer hover:underline">Sign in</a>
        </div>
    </form>
</div>

{% endblock %}
