<!--TEMPLATE/ACCOUNTS/PASSWORD_CHANGE.HTML-->
{% extends 'account/base_accounts.html' %}
{% load static %}

{% block account_title %}Change Password{% endblock %}

{% block account_content %}
<div x-data="{ showOldPassword: false, showNewPassword: false, showNewPassword2: false }">
    <form method="post" action="{% url 'account_change_password' %}">
        {% csrf_token %}

        {% if form.non_field_errors %}
        <div class="alert alert-error mb-4">
            {% for error in form.non_field_errors %}
                <div>{{ error }}</div>
            {% endfor %}
        </div>
        {% endif %}

        <div class="form-control mb-4">
            <label class="label" for="id_oldpassword">
                <p class="font-poppins text-xs md:text-sm">Current Password</p>
            </label>
            <div class="relative">
                <input :type="showOldPassword ? 'text' : 'password'" name="oldpassword" id="id_oldpassword"
                       placeholder="••••••••"
                       class="input w-full my-input pr-10 {% if form.oldpassword.errors %}input-error{% endif %}"
                       required>
                <button type="button" @click="showOldPassword = !showOldPassword"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                        tabindex="-1">
                    <i class="fa-regular" :class="showOldPassword ? 'fa-eye' : 'fa-eye-slash'"></i>
                </button>
            </div>
            {% if form.oldpassword.errors %}
                <label class="label">
                    <span class="label-text-alt text-error">{{ form.oldpassword.errors.0 }}</span>
                </label>
            {% endif %}
        </div>

        <div class="form-control mb-4">
            <label class="label" for="id_password1">
                <p class="font-poppins text-xs md:text-sm">New Password</p>
            </label>
            <div class="relative">
                <input :type="showNewPassword ? 'text' : 'password'" name="password1" id="id_password1"
                       placeholder="••••••••"
                       class="input w-full my-input pr-10 {% if form.password1.errors %}input-error{% endif %}"
                       required>
                <button type="button" @click="showNewPassword = !showNewPassword"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                        tabindex="-1">
                    <i class="fa-regular" :class="showNewPassword ? 'fa-eye' : 'fa-eye-slash'"></i>
                </button>
            </div>
            {% if form.password1.errors %}
                <label class="label">
                    <span class="label-text-alt text-error">{{ form.password1.errors.0 }}</span>
                </label>
            {% endif %}
        </div>

        <div class="form-control mb-6">
            <label class="label" for="id_password2">
                <p class="font-poppins text-xs md:text-sm">Confirm New Password</p>
            </label>
            <div class="relative">
                <input :type="showNewPassword2 ? 'text' : 'password'" name="password2" id="id_password2"
                       placeholder="••••••••"
                       class="input w-full my-input pr-10 {% if form.password2.errors %}input-error{% endif %}"
                       required>
                <button type="button" @click="showNewPassword2 = !showNewPassword2"
                        class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                        tabindex="-1">
                    <i class="fa-regular" :class="showNewPassword2 ? 'fa-eye' : 'fa-eye-slash'"></i>
                </button>
            </div>
            {% if form.password2.errors %}
                <label class="label">
                    <span class="label-text-alt text-error">{{ form.password2.errors.0 }}</span>
                </label>
            {% endif %}
        </div>

        <div class="flex justify-between">
            <a href="{% url 'account:profile' %}" class="btn btn-outline">
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">Change Password</button>
        </div>
    </form>
</div>
{% endblock %}
