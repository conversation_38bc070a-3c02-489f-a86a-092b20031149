{% extends 'dashboard/base_dashboard.html' %}

{% block dashboard_content %}
<div class="max-w-4xl mx-auto" x-data="questionForm()">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="my-h1">Add Question to "{{ survey.title }}"</h1>
        <p class="my-p text-base-content/60">Create a new question for your survey</p>
    </div>

    <!-- Question Form -->
    <div class="my-card">
        <form method="post" id="question-form">
            {% csrf_token %}
            
            <div class="space-y-6">
                <!-- Question Type -->
                <div>
                    <label for="{{ form.question_type.id_for_label }}" class="my-h3 block mb-2">
                        Question Type
                        <span class="text-error">*</span>
                    </label>
                    <select name="{{ form.question_type.name }}" 
                            id="{{ form.question_type.id_for_label }}"
                            class="select select-bordered w-full"
                            x-model="questionType"
                            @change="toggleChoicesSection()">
                        {% for value, label in form.question_type.field.choices %}
                            <option value="{{ value }}" {% if form.question_type.value == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                    {% if form.question_type.errors %}
                        <div class="text-error text-sm mt-1">
                            {{ form.question_type.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <!-- Question Content -->
                <div>
                    <label for="{{ form.content.id_for_label }}" class="my-h3 block mb-2">
                        Question Text
                        <span class="text-error">*</span>
                    </label>
                    {{ form.content }}
                    {% if form.content.errors %}
                        <div class="text-error text-sm mt-1">
                            {{ form.content.errors.0 }}
                        </div>
                    {% endif %}
                    <p class="my-p text-base-content/60 mt-1">
                        {{ form.content.help_text }}
                    </p>
                </div>

                <!-- Question Settings -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="{{ form.order.id_for_label }}" class="my-h3 block mb-2">
                            Order
                        </label>
                        {{ form.order }}
                        {% if form.order.errors %}
                            <div class="text-error text-sm mt-1">
                                {{ form.order.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="flex items-center gap-3">
                        {{ form.is_required }}
                        <label for="{{ form.is_required.id_for_label }}" class="my-h3">
                            Required Question
                        </label>
                    </div>
                </div>

                <!-- Radio Choices Section -->
                <div x-show="questionType === 'RADIO'" x-transition class="space-y-4">
                    <h3 class="my-h3">Answer Choices</h3>
                    <p class="my-p text-base-content/60">Add at least 2 choices for radio button questions</p>
                    
                    {{ formset.management_form }}
                    
                    <div id="choices-container">
                        {% for form in formset %}
                            <div class="choice-form border border-base-300 rounded-lg p-4 mb-4">
                                {% for hidden in form.hidden_fields %}
                                    {{ hidden }}
                                {% endfor %}
                                
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                                    <div class="md:col-span-2">
                                        <label class="my-h3 block mb-2">Choice Text</label>
                                        {{ form.content }}
                                        {% if form.content.errors %}
                                            <div class="text-error text-sm mt-1">
                                                {{ form.content.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div>
                                        <label class="my-h3 block mb-2">Order</label>
                                        {{ form.order }}
                                        {% if form.order.errors %}
                                            <div class="text-error text-sm mt-1">
                                                {{ form.order.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                {% if form.DELETE %}
                                    <div class="mt-3">
                                        {{ form.DELETE }}
                                        <label for="{{ form.DELETE.id_for_label }}" class="text-error">
                                            Delete this choice
                                        </label>
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-between items-center mt-8 pt-6 border-t border-base-300">
                <a href="{% url 'dashboard:survey_detail' survey.slug %}" class="btn btn-outline">
                    Cancel
                </a>
                <button type="submit" class="btn btn-primary">
                    Add Question
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function questionForm() {
    return {
        questionType: '{{ form.question_type.value|default:"RADIO" }}',
        
        init() {
            this.toggleChoicesSection();
        },
        
        toggleChoicesSection() {
            // This function can be extended for additional logic
            console.log('Question type changed to:', this.questionType);
        }
    }
}
</script>
{% endblock %}

{% block extra_js %}
{{ block.super }}
{{ form.media }}
{% endblock %}
