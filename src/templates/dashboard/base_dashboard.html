{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard - {{ block.super }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-base-200">
    <!-- Dashboard Navigation -->
    <div class="navbar bg-base-100 shadow-sm">
        <div class="container mx-auto px-4">
            <div class="flex-1">
                <a href="{% url 'dashboard:home' %}" class="btn btn-ghost text-xl">
                    📊 Dashboard
                </a>
            </div>
            <div class="flex-none">
                <ul class="menu menu-horizontal px-1">
                    <li><a href="{% url 'dashboard:home' %}">Home</a></li>
                    <li><a href="{% url 'dashboard:survey_list' %}">Surveys</a></li>
                    <li>
                        <details>
                            <summary>Create</summary>
                            <ul class="p-2 bg-base-100 rounded-t-none">
                                <li><a href="{% url 'dashboard:survey_create' %}">New Survey</a></li>
                            </ul>
                        </details>
                    </li>
                    <li><a href="{% url 'home' %}">Back to Site</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        {% if messages %}
            {% for message in messages %}
            <div class="alert alert-{{ message.tags|default:'info' }} mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                    {% if message.tags == 'success' %}
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    {% elif message.tags == 'error' %}
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    {% else %}
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    {% endif %}
                </svg>
                <span>{{ message }}</span>
            </div>
            {% endfor %}
        {% endif %}

        {% block dashboard_content %}
        {% endblock %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
{% endblock %}
