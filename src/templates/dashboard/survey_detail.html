{% extends 'dashboard/base_dashboard.html' %}

{% block dashboard_content %}
<div class="max-w-6xl mx-auto">
    <!-- Page Header -->
    <div class="flex justify-between items-start mb-8">
        <div>
            <h1 class="my-h1">{{ survey.title }}</h1>
            <p class="my-p text-base-content/60">{{ survey.description|striptags|truncatewords:20 }}</p>
            <div class="flex items-center gap-4 mt-2">
                {% if survey.is_published %}
                    <span class="badge badge-success">Published</span>
                {% else %}
                    <span class="badge badge-warning">Draft</span>
                {% endif %}
                <span class="text-sm text-base-content/60">Created {{ survey.created_at|date:"M d, Y" }}</span>
            </div>
        </div>
        <div class="flex gap-2">
            {% if survey.is_published %}
                <a href="{% url 'survey:survey_detail' survey.slug %}" 
                   class="btn btn-outline" target="_blank">
                    👀 View Public
                </a>
                <form method="post" action="{% url 'dashboard:survey_toggle_publish' survey.slug %}" class="inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-warning">
                        📝 Unpublish
                    </button>
                </form>
            {% else %}
                <form method="post" action="{% url 'dashboard:survey_toggle_publish' survey.slug %}" class="inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-success">
                        🚀 Publish
                    </button>
                </form>
            {% endif %}
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="my-card text-center">
            <div class="text-2xl font-bold text-primary">{{ questions.count }}</div>
            <div class="my-p text-base-content/60">Questions</div>
        </div>
        <div class="my-card text-center">
            <div class="text-2xl font-bold text-info">{{ stats.total_responses }}</div>
            <div class="my-p text-base-content/60">Total Responses</div>
        </div>
        <div class="my-card text-center">
            <div class="text-2xl font-bold text-success">{{ stats.completion_rate|floatformat:1 }}%</div>
            <div class="my-p text-base-content/60">Completion Rate</div>
        </div>
    </div>

    <!-- Questions Section -->
    <div class="my-card">
        <div class="flex justify-between items-center mb-6">
            <h2 class="my-h2">Questions</h2>
            <a href="{% url 'dashboard:question_create' survey.slug %}" class="btn btn-primary">
                ➕ Add Question
            </a>
        </div>

        {% if questions %}
            <div class="space-y-4">
                {% for question in questions %}
                <div class="border border-base-300 rounded-lg p-4">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-2">
                                <span class="badge badge-outline">{{ question.get_question_type_display }}</span>
                                <span class="text-sm text-base-content/60">Order: {{ question.order }}</span>
                                {% if question.is_required %}
                                    <span class="badge badge-error badge-sm">Required</span>
                                {% endif %}
                            </div>
                            <div class="my-p">{{ question.content|striptags|truncatewords:15 }}</div>
                            
                            {% if question.question_type == 'RADIO' %}
                                <div class="mt-3">
                                    <p class="text-sm text-base-content/60 mb-2">Choices:</p>
                                    <div class="flex flex-wrap gap-2">
                                        {% for choice in question.choices.all|slice:":3" %}
                                            <span class="badge badge-ghost">{{ choice.content|truncatewords:3 }}</span>
                                        {% endfor %}
                                        {% if question.choices.count > 3 %}
                                            <span class="badge badge-ghost">+{{ question.choices.count|add:"-3" }} more</span>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="flex gap-2 ml-4">
                            <a href="{% url 'dashboard:question_edit' survey.slug question.id %}" 
                               class="btn btn-outline btn-sm">
                                ✏️ Edit
                            </a>
                            <form method="post" 
                                  action="{% url 'dashboard:question_delete' survey.slug question.id %}"
                                  onsubmit="return confirm('Are you sure you want to delete this question?')"
                                  class="inline">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-error btn-sm">
                                    🗑️ Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-12">
                <div class="text-6xl mb-4">📝</div>
                <h3 class="my-h3 mb-2">No Questions Yet</h3>
                <p class="my-p text-base-content/60 mb-6">Add your first question to get started</p>
                <a href="{% url 'dashboard:question_create' survey.slug %}" class="btn btn-primary">
                    Add First Question
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Survey Actions -->
    <div class="my-card mt-6">
        <h2 class="my-h2 mb-4">Survey Actions</h2>
        <div class="flex flex-wrap gap-4">
            <a href="{% url 'dashboard:question_create' survey.slug %}" class="btn btn-outline">
                ➕ Add Question
            </a>
            {% if survey.is_published %}
                <a href="{% url 'survey:survey_detail' survey.slug %}" 
                   class="btn btn-outline" target="_blank">
                    👀 View Public Survey
                </a>
            {% endif %}
            <button class="btn btn-outline" onclick="copyToClipboard('{{ request.build_absolute_uri }}{% url 'survey:survey_detail' survey.slug %}')">
                📋 Copy Link
            </button>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('Survey link copied to clipboard!');
    });
}
</script>
{% endblock %}
