{% extends 'dashboard/base_dashboard.html' %}

{% block dashboard_content %}
<div class="max-w-7xl mx-auto">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="my-h1">Dashboard Overview</h1>
        <p class="my-p text-base-content/60">Manage your surveys and track activity</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="my-card text-center">
            <div class="text-3xl font-bold text-primary">{{ stats.total_surveys }}</div>
            <div class="my-p text-base-content/60">Total Surveys</div>
        </div>
        <div class="my-card text-center">
            <div class="text-3xl font-bold text-success">{{ stats.published_surveys }}</div>
            <div class="my-p text-base-content/60">Published</div>
        </div>
        <div class="my-card text-center">
            <div class="text-3xl font-bold text-info">{{ stats.total_responses }}</div>
            <div class="my-p text-base-content/60">Total Responses</div>
        </div>
        <div class="my-card text-center">
            <div class="text-3xl font-bold text-warning">{{ stats.completed_responses }}</div>
            <div class="my-p text-base-content/60">Completed</div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Surveys -->
        <div class="my-card">
            <div class="flex justify-between items-center mb-4">
                <h2 class="my-h2">Recent Surveys</h2>
                <a href="{% url 'dashboard:survey_create' %}" class="btn btn-primary btn-sm">
                    Create New
                </a>
            </div>
            
            {% if surveys %}
                <div class="space-y-3">
                    {% for survey in surveys %}
                    <div class="flex items-center justify-between p-3 bg-base-200 rounded-lg">
                        <div class="flex-1">
                            <h3 class="my-h3">{{ survey.title }}</h3>
                            <p class="my-p text-base-content/60">
                                {{ survey.total_questions }} question{{ survey.total_questions|pluralize }}
                                {% if survey.is_published %}
                                    <span class="badge badge-success badge-sm ml-2">Published</span>
                                {% else %}
                                    <span class="badge badge-warning badge-sm ml-2">Draft</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="flex gap-2">
                            <a href="{% url 'dashboard:survey_detail' survey.slug %}" 
                               class="btn btn-outline btn-sm">
                                Manage
                            </a>
                            {% if survey.is_published %}
                                <a href="{% url 'survey:survey_detail' survey.slug %}" 
                                   class="btn btn-primary btn-sm" target="_blank">
                                    View
                                </a>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="mt-4 text-center">
                    <a href="{% url 'dashboard:survey_list' %}" class="btn btn-outline">
                        View All Surveys
                    </a>
                </div>
            {% else %}
                <div class="text-center py-8">
                    <p class="my-p text-base-content/60 mb-4">No surveys created yet</p>
                    <a href="{% url 'dashboard:survey_create' %}" class="btn btn-primary">
                        Create Your First Survey
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Recent Activity -->
        <div class="my-card">
            <h2 class="my-h2 mb-4">Recent Activity</h2>
            
            {% if recent_activities %}
                <div class="space-y-3">
                    {% for activity in recent_activities %}
                    <div class="flex items-start gap-3 p-3 bg-base-200 rounded-lg">
                        <div class="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                        <div class="flex-1">
                            <p class="my-p">{{ activity.description }}</p>
                            <p class="text-xs text-base-content/50">{{ activity.created_at|timesince }} ago</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <p class="my-p text-base-content/60">No recent activity</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="my-card mt-8">
        <h2 class="my-h2 mb-4">Quick Actions</h2>
        <div class="flex flex-wrap gap-4">
            <a href="{% url 'dashboard:survey_create' %}" class="btn btn-primary">
                📝 Create Survey
            </a>
            <a href="{% url 'dashboard:survey_list' %}" class="btn btn-outline">
                📋 Manage Surveys
            </a>
            <a href="{% url 'survey:survey_list' %}" class="btn btn-outline" target="_blank">
                👀 View Public Surveys
            </a>
        </div>
    </div>
</div>
{% endblock %}
