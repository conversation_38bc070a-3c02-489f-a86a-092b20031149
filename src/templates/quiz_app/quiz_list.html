{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="max-w-7xl mx-auto space-y-4 md:space-y-6">
    <!-- Header -->
    <div class="my-card">
        <h1 class="my-h1">Available Quizzes</h1>
        <p class="my-p">Choose from our collection of quizzes to test your knowledge.</p>
    </div>

    <!-- Filters -->
        <div class="my-card">
        <div class="flex flex-col md:flex-row gap-4 items-center">
            <!-- Search -->
            <div class="flex-1">
                <form method="get" class="flex gap-2">
                    <input
                        type="text"
                        name="search"
                        value="{{ search_query }}"
                        placeholder="Search quizzes..."
                        class="my-input flex-1"
                    >
                    <button type="submit" class="btn btn-primary">
                        <i class="fa-regular fa-search"></i>
                        Search
                    </button>
                </form>
            </div>

        </div>
    </div>
    <div class="my-card">
        <div class="flex flex-col md:flex-row gap-4 items-center">

            <!-- Category Filter -->
            <div class="flex gap-2 flex-wrap">
                <a href="{% url 'quiz_app:quiz_list' %}" 
                   class="btn {% if not current_category %}btn-primary{% else %}btn-outline{% endif %}">
                    All Categories
                </a>
                {% for category in categories %}
                <a href="{% url 'quiz_app:quiz_list' %}?category={{ category.slug }}" 
                   class="btn {% if current_category == category.slug %}btn-primary{% else %}btn-outline{% endif %}">
                    {{ category.name }} ({{ category.quiz_count }})
                </a>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Quiz Grid -->
    {% if quizzes %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for quiz in quizzes %}
        <div class="my-card hover:bg-base-200 transition-colors">
            <div class="flex flex-col h-full">
                <!-- Quiz Info -->
                <div class="flex-1">
                    <div class="flex items-start justify-between mb-2">
                        <span class="badge badge-primary">{{ quiz.category.name }}</span>
                        <span class="text-xs text-base-content/60">{{ quiz.total_questions }} questions</span>
                    </div>
                    
                    <h3 class="my-h2 mb-2">{{ quiz.title }}</h3>
                    <p class="my-p text-base-content/80 mb-4">{{ quiz.description|truncatewords:20 }}</p>
                </div>
                
                <!-- Quiz Stats -->
                <div class="flex items-center justify-between text-xs text-base-content/60 mb-4">
                    <span><i class="fa-regular fa-clock"></i> 
                        {% if quiz.time_limit %}{{ quiz.time_limit }} min{% else %}No limit{% endif %}
                    </span>
                    <span><i class="fa-regular fa-target"></i> Pass: {{ quiz.pass_mark }}%</span>
                </div>
                
                <!-- Action Button -->
                <a href="{% url 'quiz_app:quiz_detail' slug=quiz.slug %}" 
                   class="btn btn-primary w-full">
                    <i class="fa-regular fa-play"></i>
                    View Quiz
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="flex justify-center mt-8">
        <div class="join">
            {% if page_obj.has_previous %}
                <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}" 
                   class="join-item btn">«</a>
                <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}" 
                   class="join-item btn">‹</a>
            {% endif %}
            
            <span class="join-item btn btn-active">{{ page_obj.number }}</span>
            
            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}" 
                   class="join-item btn">›</a>
                <a href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}" 
                   class="join-item btn">»</a>
            {% endif %}
        </div>
    </div>
    {% endif %}

    {% else %}
    <div class="my-card text-center">
        <div class="py-12">
            <i class="fa-regular fa-clipboard-question text-6xl text-base-content/30 mb-4"></i>
            <h3 class="my-h2 mb-2">No Quizzes Found</h3>
            <p class="my-p">
                {% if search_query %}
                    No quizzes match your search "{{ search_query }}".
                {% elif current_category %}
                    No quizzes found in this category.
                {% else %}
                    No quizzes are currently available.
                {% endif %}
            </p>
            {% if search_query or current_category %}
            <a href="{% url 'quiz_app:quiz_list' %}" class="btn btn-primary mt-4">
                View All Quizzes
            </a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
