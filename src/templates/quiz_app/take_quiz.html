{% extends 'quiz_app/base_quiz.html' %}
{% load static %}

{% block quiz_content %}
<div class="max-w-4xl mx-auto" x-data="quizTaker()">
    <!-- Quiz Header -->
    <div class="my-card">
        <div class="flex items-center justify-between mb-4">
            <div>
                <h1 class="my-h2">{{ session.quiz.title }}</h1>
                <p class="my-p text-base-content/60">{{ session.quiz.category.name }}</p>
            </div>
            {% if session.quiz.time_limit %}
            <div class="text-right">
                <div class="text-sm text-base-content/60">Time Remaining</div>
                <div class="text-lg font-bold text-primary" x-text="timeRemaining">
                    {{ session.quiz.time_limit }}:00
                </div>
            </div>
            {% endif %}
        </div>
        
        <!-- Progress Bar -->
        <div class="w-full bg-base-300 rounded-full h-2 mb-2">
            <div class="bg-primary h-2 rounded-full transition-all duration-300" 
                 style="width: {{ progress.percentage }}%"></div>
        </div>
        <div class="flex justify-between text-xs text-base-content/60">
            <span>Question {{ progress.current }} of {{ progress.total }}</span>
            <span>{{ progress.percentage|floatformat:0 }}% Complete</span>
        </div>
    </div>

    <!-- Question Content -->
    <div id="question-container" 
         hx-target="this" 
         hx-swap="innerHTML"
         hx-indicator="#loading-indicator">
        {% include 'quiz_app/partials/question_content.html' %}
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator">
        <div class="my-card text-center">
            <div class="loading loading-spinner loading-lg text-primary"></div>
            <p class="my-p mt-2">Loading next question...</p>
        </div>
    </div>
</div>

<!-- Alpine.js Quiz Taker Component -->
<script>
function quizTaker() {
    return {
        timeRemaining: '{{ session.quiz.time_limit }}:00',
        timeLimit: {{ session.quiz.time_limit|default:0 }},
        startTime: new Date(),
        
        init() {
            if (this.timeLimit > 0) {
                this.startTimer();
            }
        },
        
        startTimer() {
            const timer = setInterval(() => {
                const elapsed = Math.floor((new Date() - this.startTime) / 1000);
                const remaining = (this.timeLimit * 60) - elapsed;
                
                if (remaining <= 0) {
                    clearInterval(timer);
                    this.timeRemaining = '0:00';
                    this.submitQuiz();
                } else {
                    const minutes = Math.floor(remaining / 60);
                    const seconds = remaining % 60;
                    this.timeRemaining = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                }
            }, 1000);
        },
        
        submitQuiz() {
            // Auto-submit the quiz when time runs out
            const form = document.querySelector('#question-form');
            if (form) {
                htmx.trigger(form, 'submit');
            }
        }
    }
}
</script>
{% endblock %}
