{% extends 'quiz_app/base_quiz.html' %}
{% load static %}

{% block quiz_content %}
<div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="my-card text-center">
        <h1 class="my-h1">Quiz Categories</h1>
        <p class="my-p">Browse quizzes by category to find topics that interest you.</p>
    </div>

    <!-- Categories Grid -->
    {% if categories %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for category in categories %}
        <div class="my-card hover:bg-base-200 transition-colors">
            <div class="text-center">
                <!-- Category Icon -->
                <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fa-regular fa-folder text-primary text-2xl"></i>
                </div>
                
                <!-- Category Info -->
                <h3 class="my-h2 mb-2">{{ category.name }}</h3>
                {% if category.description %}
                <p class="my-p text-base-content/80 mb-4">{{ category.description|truncatewords:15 }}</p>
                {% endif %}
                
                <!-- Quiz Count -->
                <div class="flex items-center justify-center gap-2 mb-4">
                    <i class="fa-regular fa-clipboard-question text-primary"></i>
                    <span class="my-p">{{ category.quiz_count }} quiz{{ category.quiz_count|pluralize }}</span>
                </div>
                
                <!-- Action Button -->
                <a href="{% url 'quiz_app:quiz_list' %}?category={{ category.slug }}" 
                   class="btn btn-primary w-full">
                    <i class="fa-regular fa-arrow-right"></i>
                    View Quizzes
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="my-card text-center">
        <div class="py-12">
            <i class="fa-regular fa-folder-open text-6xl text-base-content/30 mb-4"></i>
            <h3 class="my-h2 mb-2">No Categories Available</h3>
            <p class="my-p">There are currently no quiz categories available.</p>
        </div>
    </div>
    {% endif %}

    <!-- Back to Quizzes -->
    <div class="text-center mt-8">
        <a href="{% url 'quiz_app:quiz_list' %}" class="btn btn-ghost">
            <i class="fa-regular fa-arrow-left"></i>
            Back to All Quizzes
        </a>
    </div>
</div>
{% endblock %}
