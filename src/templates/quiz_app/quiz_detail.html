{% extends 'quiz_app/base_quiz.html' %}
{% load static %}

{% block quiz_content %}
<div class="max-w-4xl mx-auto">
    <!-- Quiz Header -->
    <div class="my-card">
        <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
                <div class="flex items-center gap-2 mb-2">
                    <span class="badge badge-primary">{{ quiz.category.name }}</span>
                    {% if not quiz.is_published %}
                    <span class="badge badge-warning">Draft</span>
                    {% endif %}
                </div>
                <h1 class="my-h1">{{ quiz.title }}</h1>
                <p class="my-p">{{ quiz.description }}</p>
            </div>
        </div>

        <!-- Quiz Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-primary">{{ quiz.total_questions }}</div>
                <div class="text-xs text-base-content/60">Questions</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-primary">
                    {% if quiz.time_limit %}{{ quiz.time_limit }}{% else %}∞{% endif %}
                </div>
                <div class="text-xs text-base-content/60">Minutes</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-primary">{{ quiz.pass_mark }}%</div>
                <div class="text-xs text-base-content/60">Pass Mark</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-primary">{{ total_attempts }}</div>
                <div class="text-xs text-base-content/60">Attempts</div>
            </div>
        </div>
    </div>

    <!-- Quiz Settings -->
    <div class="my-card">
        <h2 class="my-h2 mb-4">Quiz Information</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center gap-2">
                <i class="fa-regular fa-shuffle text-primary"></i>
                <span class="my-p">
                    {% if quiz.random_order %}Random question order{% else %}Fixed question order{% endif %}
                </span>
            </div>
            <div class="flex items-center gap-2">
                <i class="fa-regular fa-repeat text-primary"></i>
                <span class="my-p">
                    {% if quiz.single_attempt %}Single attempt only{% else %}Multiple attempts allowed{% endif %}
                </span>
            </div>
            <div class="flex items-center gap-2">
                <i class="fa-regular fa-eye text-primary"></i>
                <span class="my-p">
                    {% if quiz.show_answers_at_end %}Answers shown at end{% else %}No answers shown{% endif %}
                </span>
            </div>
            <div class="flex items-center gap-2">
                <i class="fa-regular fa-check-circle text-primary"></i>
                <span class="my-p">
                    {% if quiz.show_correct_answers %}Correct answers highlighted{% else %}No answer feedback{% endif %}
                </span>
            </div>
        </div>
    </div>

    {% if user.is_authenticated %}
    <!-- User Status -->
    {% if existing_session %}
    <div class="my-card">
        <h2 class="my-h2 mb-4">Your Progress</h2>
        {% if existing_session.is_complete %}
        <div class="alert {% if existing_session.passed %}alert-success{% else %}alert-warning{% endif %} mb-4">
            <div>
                <i class="fa-regular fa-{% if existing_session.passed %}check-circle{% else %}exclamation-triangle{% endif %}"></i>
                <span>
                    You completed this quiz on {{ existing_session.completed_at|date:"M d, Y" }} 
                    with a score of {{ existing_session.score_percentage|floatformat:1 }}%
                    {% if existing_session.passed %}(Passed){% else %}(Failed){% endif %}
                </span>
            </div>
        </div>
        {% else %}
        <div class="alert alert-info mb-4">
            <div>
                <i class="fa-regular fa-clock"></i>
                <span>You have an incomplete session. You can continue where you left off.</span>
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="my-card">
        {% if can_retake %}
        <div class="flex flex-col sm:flex-row gap-4">
            {% if existing_session and not existing_session.is_complete %}
            <a href="{% url 'quiz_app:take_quiz' session_id=existing_session.id %}" 
               class="btn btn-primary flex-1">
                <i class="fa-regular fa-play"></i>
                Continue Quiz
            </a>
            {% endif %}
            
            <a href="{% url 'quiz_app:start_quiz' slug=quiz.slug %}" 
               class="btn {% if existing_session and not existing_session.is_complete %}btn-outline{% else %}btn-primary{% endif %} flex-1"
               {% if existing_session and not existing_session.is_complete %}
               onclick="return confirm('This will start a new quiz session and discard your current progress. Are you sure?')"
               {% endif %}>
                <i class="fa-regular fa-{% if existing_session and not existing_session.is_complete %}refresh{% else %}play{% endif %}"></i>
                {% if existing_session and not existing_session.is_complete %}Start Over{% else %}Start Quiz{% endif %}
            </a>
        </div>
        {% else %}
        <div class="alert alert-warning">
            <div>
                <i class="fa-regular fa-lock"></i>
                <span>You have already completed this quiz and cannot retake it.</span>
            </div>
        </div>
        {% endif %}
        
        <div class="flex justify-center mt-4">
            <a href="{% url 'quiz_app:quiz_list' %}" class="btn btn-ghost">
                <i class="fa-regular fa-arrow-left"></i>
                Back to Quizzes
            </a>
        </div>
    </div>

    {% else %}
    <!-- Login Required -->
    <div class="my-card text-center">
        <div class="py-8">
            <i class="fa-regular fa-user-lock text-6xl text-base-content/30 mb-4"></i>
            <h3 class="my-h2 mb-2">Login Required</h3>
            <p class="my-p mb-6">You need to be logged in to take quizzes.</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{% url 'account:login' %}" class="btn btn-primary">
                    <i class="fa-regular fa-sign-in"></i>
                    Login
                </a>
                <a href="{% url 'account:signup' %}" class="btn btn-outline">
                    <i class="fa-regular fa-user-plus"></i>
                    Sign Up
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
