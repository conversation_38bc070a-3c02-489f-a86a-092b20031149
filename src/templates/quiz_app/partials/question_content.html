{% load static %}

<div class="my-card">
    <!-- Question Header -->
    <div class="flex items-start justify-between mb-6">
        <div class="flex-1">
            <div class="flex items-center gap-2 mb-2">
                <span class="badge badge-primary">{{ question.get_question_type_display }}</span>
                <span class="badge badge-outline">{{ question.marks }} point{{ question.marks|pluralize }}</span>
                {% if question.category %}
                <span class="badge badge-ghost">{{ question.category.name }}</span>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Question Content -->
    <div class="mb-6">
        <h2 class="my-h2 mb-4">{{ question.content }}</h2>
        
        {% if question.image %}
        <div class="mb-4">
            <img src="{{ question.image.url }}" 
                 alt="Question image" 
                 class="max-w-full h-auto rounded border">
        </div>
        {% endif %}
        
        {% if question.external_link %}
        <div class="mb-4">
            <a href="{{ question.external_link }}" 
               target="_blank" 
               class="btn btn-outline btn-sm">
                <i class="fa-regular fa-external-link"></i>
                Reference Link
            </a>
        </div>
        {% endif %}
    </div>

    <!-- Answer Form -->
    <form id="question-form" 
          method="post" 
          hx-post="{% url 'quiz_app:take_quiz' session_id=session.id %}"
          hx-target="#question-container"
          hx-swap="innerHTML"
          hx-indicator="#loading-indicator">
        {% csrf_token %}
        
        {% if question.question_type == 'MCQ' %}
        <!-- Multiple Choice Options -->
        <div class="space-y-3 mb-6">
            {% for choice in question.choices.all %}
            <label class="flex items-start gap-3 p-4 border border-base-300 rounded hover:bg-base-200 cursor-pointer transition-colors">
                <input type="radio" 
                       name="choice" 
                       value="{{ choice.id }}"
                       class="radio radio-primary mt-1"
                       {% if existing_answer.selected_choice.id == choice.id %}checked{% endif %}
                       required>
                <span class="flex-1 my-p">{{ choice.content }}</span>
            </label>
            {% endfor %}
        </div>
        
        {% elif question.question_type == 'SUBJECTIVE' %}
        <!-- Subjective Answer -->
        <div class="mb-6">
            <label class="block mb-2">
                <span class="my-p font-medium">Your Answer:</span>
            </label>
            <textarea name="text_answer" 
                      rows="6" 
                      class="my-input"
                      placeholder="Enter your answer here..."
                      required>{% if existing_answer %}{{ existing_answer.text_answer }}{% endif %}</textarea>
            <div class="text-xs text-base-content/60 mt-1">
                <i class="fa-regular fa-info-circle"></i>
                Subjective answers will be reviewed manually.
            </div>
        </div>
        {% endif %}

        <!-- Navigation Buttons -->
        <div class="flex justify-between items-center">
            <div>
                {% if session.current_question_index > 0 %}
                <button type="button" 
                        class="btn btn-ghost"
                        onclick="history.back()">
                    <i class="fa-regular fa-arrow-left"></i>
                    Previous
                </button>
                {% endif %}
            </div>
            
            <div class="flex gap-2">
                <button type="submit" 
                        class="btn btn-primary">
                    {% if progress.current == progress.total %}
                        <i class="fa-regular fa-check"></i>
                        Finish Quiz
                    {% else %}
                        <i class="fa-regular fa-arrow-right"></i>
                        Next Question
                    {% endif %}
                </button>
            </div>
        </div>
    </form>

    <!-- Question Navigation -->
    {% if progress.total > 1 %}
    <div class="mt-6 pt-6 border-t border-base-300">
        <div class="flex items-center justify-between mb-2">
            <span class="my-p text-sm">Question Navigation:</span>
            <span class="text-xs text-base-content/60">{{ progress.current }} / {{ progress.total }}</span>
        </div>
        <div class="flex flex-wrap gap-1">
            {% for i in session.question_order %}
            <div class="w-8 h-8 flex items-center justify-center text-xs rounded
                        {% if forloop.counter0 == session.current_question_index %}
                            bg-primary text-primary-content
                        {% elif forloop.counter0 < session.current_question_index %}
                            bg-success text-success-content
                        {% else %}
                            bg-base-300 text-base-content
                        {% endif %}">
                {{ forloop.counter }}
            </div>
            {% endfor %}
        </div>
        <div class="flex items-center gap-4 mt-2 text-xs">
            <div class="flex items-center gap-1">
                <div class="w-3 h-3 bg-success rounded"></div>
                <span>Completed</span>
            </div>
            <div class="flex items-center gap-1">
                <div class="w-3 h-3 bg-primary rounded"></div>
                <span>Current</span>
            </div>
            <div class="flex items-center gap-1">
                <div class="w-3 h-3 bg-base-300 rounded"></div>
                <span>Remaining</span>
            </div>
        </div>
    </div>
    {% endif %}
</div>
