{% extends 'quiz_app/base_quiz.html' %}
{% load static %}

{% block quiz_content %}
<div class="max-w-4xl mx-auto">
    <!-- Results Header -->
    <div class="my-card text-center">
        <div class="mb-6">
            {% if passed %}
            <div class="w-20 h-20 bg-success/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fa-regular fa-trophy text-success text-3xl"></i>
            </div>
            <h1 class="my-h1 text-success">Congratulations!</h1>
            <p class="my-p">{{ quiz.success_text }}</p>
            {% else %}
            <div class="w-20 h-20 bg-warning/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fa-regular fa-exclamation-triangle text-warning text-3xl"></i>
            </div>
            <h1 class="my-h1 text-warning">Quiz Complete</h1>
            <p class="my-p">{{ quiz.fail_text }}</p>
            {% endif %}
        </div>

        <!-- Score Display -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <div class="text-center">
                <div class="text-4xl font-bold {% if passed %}text-success{% else %}text-warning{% endif %}">
                    {{ score_percentage|floatformat:1 }}%
                </div>
                <div class="text-sm text-base-content/60">Final Score</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-primary">
                    {{ session.score }}/{{ session.max_possible_score }}
                </div>
                <div class="text-sm text-base-content/60">Points Earned</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-primary">
                    {{ session.time_spent.total_seconds|floatformat:0 }}s
                </div>
                <div class="text-sm text-base-content/60">Time Taken</div>
            </div>
        </div>
    </div>

    <!-- Quiz Summary -->
    <div class="my-card">
        <h2 class="my-h2 mb-4">Quiz Summary</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-base-200 rounded">
                <div class="text-2xl font-bold text-primary">{{ answers.count }}</div>
                <div class="text-xs text-base-content/60">Total Questions</div>
            </div>
            <div class="text-center p-4 bg-base-200 rounded">
                <div class="text-2xl font-bold text-success">
                    {{ answers|length|add:0 }}
                    {% for answer in answers %}
                        {% if answer.is_correct %}+1{% endif %}
                    {% endfor %}
                </div>
                <div class="text-xs text-base-content/60">Correct Answers</div>
            </div>
            <div class="text-center p-4 bg-base-200 rounded">
                <div class="text-2xl font-bold text-error">
                    {% for answer in answers %}
                        {% if answer.is_correct == False %}+1{% endif %}
                    {% endfor %}
                </div>
                <div class="text-xs text-base-content/60">Incorrect Answers</div>
            </div>
            <div class="text-center p-4 bg-base-200 rounded">
                <div class="text-2xl font-bold text-warning">
                    {% for answer in answers %}
                        {% if answer.is_correct == None %}+1{% endif %}
                    {% endfor %}
                </div>
                <div class="text-xs text-base-content/60">Pending Review</div>
            </div>
        </div>
    </div>

    <!-- Detailed Results -->
    {% if quiz.show_answers_at_end %}
    <div class="my-card">
        <h2 class="my-h2 mb-4">Detailed Results</h2>
        <div class="space-y-4">
            {% for answer in answers %}
            <div class="border border-base-300 rounded p-4">
                <!-- Question -->
                <div class="flex items-start justify-between mb-3">
                    <div class="flex-1">
                        <div class="flex items-center gap-2 mb-2">
                            <span class="badge badge-primary">{{ answer.question.get_question_type_display }}</span>
                            <span class="badge badge-outline">{{ answer.question.marks }} point{{ answer.question.marks|pluralize }}</span>
                            {% if answer.is_correct == True %}
                            <span class="badge badge-success">Correct</span>
                            {% elif answer.is_correct == False %}
                            <span class="badge badge-error">Incorrect</span>
                            {% else %}
                            <span class="badge badge-warning">Pending Review</span>
                            {% endif %}
                        </div>
                        <h3 class="my-h3 mb-2">{{ answer.question.content }}</h3>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium">{{ answer.marks_awarded }}/{{ answer.question.marks }}</div>
                        <div class="text-xs text-base-content/60">Points</div>
                    </div>
                </div>

                <!-- User Answer -->
                <div class="mb-3">
                    <div class="text-sm font-medium mb-1">Your Answer:</div>
                    {% if answer.question.question_type == 'MCQ' %}
                        {% if answer.selected_choice %}
                        <div class="p-2 bg-base-200 rounded">
                            {{ answer.selected_choice.content }}
                        </div>
                        {% else %}
                        <div class="p-2 bg-base-200 rounded text-base-content/60">
                            No answer selected
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="p-2 bg-base-200 rounded">
                            {% if answer.text_answer %}
                                {{ answer.text_answer }}
                            {% else %}
                                <span class="text-base-content/60">No answer provided</span>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>

                <!-- Correct Answer (for MCQ) -->
                {% if answer.question.question_type == 'MCQ' and quiz.show_correct_answers %}
                <div class="mb-3">
                    <div class="text-sm font-medium mb-1">Correct Answer:</div>
                    {% for choice in answer.question.choices.all %}
                        {% if choice.is_correct %}
                        <div class="p-2 bg-success/10 border border-success/20 rounded">
                            {{ choice.content }}
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Explanation -->
                {% if answer.question.explanation %}
                <div>
                    <div class="text-sm font-medium mb-1">Explanation:</div>
                    <div class="p-2 bg-info/10 border border-info/20 rounded">
                        {{ answer.question.explanation }}
                    </div>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="my-card text-center">
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            {% if not quiz.single_attempt %}
            <a href="{% url 'quiz_app:start_quiz' slug=quiz.slug %}" 
               class="btn btn-primary">
                <i class="fa-regular fa-refresh"></i>
                Retake Quiz
            </a>
            {% endif %}
            
            <a href="{% url 'quiz_app:quiz_detail' slug=quiz.slug %}" 
               class="btn btn-outline">
                <i class="fa-regular fa-info-circle"></i>
                Quiz Details
            </a>
            
            <a href="{% url 'quiz_app:quiz_list' %}" 
               class="btn btn-ghost">
                <i class="fa-regular fa-list"></i>
                Browse Quizzes
            </a>
        </div>
    </div>
</div>
{% endblock %}
