from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.db import transaction
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
import uuid

from survey.models import Survey, Question, RadioChoice, SurveyResponse
from .models import DashboardActivity, SurveyInvitation
from .forms import SurveyForm, QuestionForm, RadioChoiceFormSet, SurveyInvitationForm


@login_required
def dashboard_home(request):
    """Dashboard home page with overview statistics."""
    # Get user's surveys
    user_surveys = Survey.objects.filter(created_by=request.user) if hasattr(Survey, 'created_by') else Survey.objects.all()[:5]

    # Get recent activities
    recent_activities = DashboardActivity.objects.filter(user=request.user)[:10]

    # Get survey statistics
    total_surveys = user_surveys.count() if hasattr(Survey, 'created_by') else Survey.objects.count()
    published_surveys = user_surveys.filter(is_published=True).count() if hasattr(Survey, 'created_by') else Survey.objects.filter(is_published=True).count()

    # Get response statistics
    total_responses = SurveyResponse.objects.filter(survey__in=user_surveys).count() if hasattr(Survey, 'created_by') else SurveyResponse.objects.count()
    completed_responses = SurveyResponse.objects.filter(survey__in=user_surveys, is_complete=True).count() if hasattr(Survey, 'created_by') else SurveyResponse.objects.filter(is_complete=True).count()

    context = {
        'surveys': user_surveys,
        'recent_activities': recent_activities,
        'stats': {
            'total_surveys': total_surveys,
            'published_surveys': published_surveys,
            'total_responses': total_responses,
            'completed_responses': completed_responses,
        }
    }

    return render(request, 'dashboard/home.html', context)


@login_required
def survey_list(request):
    """List all surveys in the dashboard."""
    surveys = Survey.objects.all().order_by('-created_at')

    return render(request, 'dashboard/survey_list.html', {
        'surveys': surveys
    })


@login_required
def survey_create(request):
    """Create a new survey."""
    if request.method == 'POST':
        form = SurveyForm(request.POST)
        if form.is_valid():
            survey = form.save()

            # Log activity
            DashboardActivity.objects.create(
                user=request.user,
                activity_type='SURVEY_CREATED',
                description=f'Created survey: {survey.title}',
                related_object_id=survey.id,
                related_object_type='Survey'
            )

            messages.success(request, f'Survey "{survey.title}" created successfully!')
            return redirect('dashboard:survey_detail', slug=survey.slug)
    else:
        form = SurveyForm()

    return render(request, 'dashboard/survey_create.html', {
        'form': form
    })


@login_required
def survey_detail(request, slug):
    """Survey detail page with questions and management options."""
    survey = get_object_or_404(Survey, slug=slug)
    questions = survey.questions.all().order_by('order', 'created_at')

    # Get survey statistics
    total_responses = survey.responses.count()
    completed_responses = survey.responses.filter(is_complete=True).count()

    context = {
        'survey': survey,
        'questions': questions,
        'stats': {
            'total_responses': total_responses,
            'completed_responses': completed_responses,
            'completion_rate': (completed_responses / total_responses * 100) if total_responses > 0 else 0
        }
    }

    return render(request, 'dashboard/survey_detail.html', context)


@login_required
def question_create(request, survey_slug):
    """Create a new question for a survey."""
    survey = get_object_or_404(Survey, slug=survey_slug)

    if request.method == 'POST':
        form = QuestionForm(request.POST)
        if form.is_valid():
            question = form.save(commit=False)
            question.survey = survey
            question.save()

            # If it's a radio question, handle choices
            if question.question_type == 'RADIO':
                formset = RadioChoiceFormSet(request.POST, instance=question)
                if formset.is_valid():
                    formset.save()

                    # Log activity
                    DashboardActivity.objects.create(
                        user=request.user,
                        activity_type='QUESTION_ADDED',
                        description=f'Added radio question to survey: {survey.title}',
                        related_object_id=question.id,
                        related_object_type='Question'
                    )

                    messages.success(request, 'Radio question created successfully!')
                    return redirect('dashboard:survey_detail', slug=survey.slug)
                else:
                    question.delete()  # Remove question if choices are invalid
                    messages.error(request, 'Please fix the choice errors below.')
            else:
                # Log activity for text question
                DashboardActivity.objects.create(
                    user=request.user,
                    activity_type='QUESTION_ADDED',
                    description=f'Added text question to survey: {survey.title}',
                    related_object_id=question.id,
                    related_object_type='Question'
                )

                messages.success(request, 'Text question created successfully!')
                return redirect('dashboard:survey_detail', slug=survey.slug)
    else:
        form = QuestionForm()
        # Set default order to next available
        next_order = survey.questions.count() + 1
        form.initial['order'] = next_order

    # Initialize empty formset for radio choices
    formset = RadioChoiceFormSet(instance=Question())

    return render(request, 'dashboard/question_create.html', {
        'survey': survey,
        'form': form,
        'formset': formset
    })


@login_required
@require_POST
def survey_toggle_publish(request, slug):
    """Toggle survey published status."""
    survey = get_object_or_404(Survey, slug=slug)

    survey.is_published = not survey.is_published
    survey.save()

    activity_type = 'SURVEY_PUBLISHED' if survey.is_published else 'SURVEY_UNPUBLISHED'
    status = 'published' if survey.is_published else 'unpublished'

    # Log activity
    DashboardActivity.objects.create(
        user=request.user,
        activity_type=activity_type,
        description=f'Survey "{survey.title}" {status}',
        related_object_id=survey.id,
        related_object_type='Survey'
    )

    messages.success(request, f'Survey "{survey.title}" has been {status}!')
    return redirect('dashboard:survey_detail', slug=survey.slug)


@login_required
def question_edit(request, survey_slug, question_id):
    """Edit an existing question."""
    survey = get_object_or_404(Survey, slug=survey_slug)
    question = get_object_or_404(Question, id=question_id, survey=survey)

    if request.method == 'POST':
        form = QuestionForm(request.POST, instance=question)
        if form.is_valid():
            question = form.save()

            # Handle radio choices if it's a radio question
            if question.question_type == 'RADIO':
                formset = RadioChoiceFormSet(request.POST, instance=question)
                if formset.is_valid():
                    formset.save()

                    # Log activity
                    DashboardActivity.objects.create(
                        user=request.user,
                        activity_type='QUESTION_UPDATED',
                        description=f'Updated radio question in survey: {survey.title}',
                        related_object_id=question.id,
                        related_object_type='Question'
                    )

                    messages.success(request, 'Question updated successfully!')
                    return redirect('dashboard:survey_detail', slug=survey.slug)
                else:
                    messages.error(request, 'Please fix the choice errors below.')
            else:
                # Log activity for text question
                DashboardActivity.objects.create(
                    user=request.user,
                    activity_type='QUESTION_UPDATED',
                    description=f'Updated text question in survey: {survey.title}',
                    related_object_id=question.id,
                    related_object_type='Question'
                )

                messages.success(request, 'Question updated successfully!')
                return redirect('dashboard:survey_detail', slug=survey.slug)
    else:
        form = QuestionForm(instance=question)

    # Initialize formset for radio choices
    formset = RadioChoiceFormSet(instance=question) if question.question_type == 'RADIO' else RadioChoiceFormSet(instance=Question())

    return render(request, 'dashboard/question_edit.html', {
        'survey': survey,
        'question': question,
        'form': form,
        'formset': formset
    })


@login_required
@require_POST
def question_delete(request, survey_slug, question_id):
    """Delete a question."""
    survey = get_object_or_404(Survey, slug=survey_slug)
    question = get_object_or_404(Question, id=question_id, survey=survey)

    question_content = question.content[:50]
    question.delete()

    # Log activity
    DashboardActivity.objects.create(
        user=request.user,
        activity_type='QUESTION_DELETED',
        description=f'Deleted question from survey: {survey.title}',
        related_object_type='Question'
    )

    messages.success(request, f'Question "{question_content}..." deleted successfully!')
    return redirect('dashboard:survey_detail', slug=survey.slug)
