from django import forms
from django.utils.text import slugify
from tinymce.widgets import TinyMCE
from survey.models import Survey, Question, RadioChoice


class SurveyForm(forms.ModelForm):
    """Form for creating and editing surveys."""

    description = forms.CharField(
        widget=TinyMCE(attrs={
            'cols': 80,
            'rows': 10,
            'class': 'tinymce-editor'
        }),
        help_text="Describe the purpose and scope of this survey"
    )
    
    class Meta:
        model = Survey
        fields = ['title', 'description']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'my-input',
                'placeholder': 'Enter survey title'
            }),
        }
    
    def save(self, commit=True):
        survey = super().save(commit=False)
        if not survey.slug:
            survey.slug = slugify(survey.title)
        if commit:
            survey.save()
        return survey


class QuestionForm(forms.ModelForm):
    """Form for creating and editing survey questions."""

    content = forms.CharField(
        widget=TinyMCE(attrs={
            'cols': 80,
            'rows': 8,
            'class': 'tinymce-editor'
        }),
        help_text="Enter the question text. You can use rich formatting."
    )
    
    class Meta:
        model = Question
        fields = ['question_type', 'content', 'is_required', 'order']
        widgets = {
            'question_type': forms.Select(attrs={
                'class': 'select select-bordered w-full',
            }),
            'order': forms.NumberInput(attrs={
                'class': 'my-input',
                'min': 1
            }),
            'is_required': forms.CheckboxInput(attrs={
                'class': 'checkbox checkbox-primary'
            }),
        }


class RadioChoiceForm(forms.ModelForm):
    """Form for creating radio button choices."""
    
    class Meta:
        model = RadioChoice
        fields = ['content', 'order']
        widgets = {
            'content': forms.TextInput(attrs={
                'class': 'my-input',
                'placeholder': 'Enter choice text'
            }),
            'order': forms.NumberInput(attrs={
                'class': 'my-input',
                'min': 1
            }),
        }


# Formset for managing multiple radio choices
RadioChoiceFormSet = forms.inlineformset_factory(
    Question,
    RadioChoice,
    form=RadioChoiceForm,
    extra=3,
    can_delete=True,
    min_num=2,
    validate_min=True
)


class SurveyInvitationForm(forms.Form):
    """Form for inviting users to participate in surveys."""
    
    emails = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'my-input',
            'rows': 4,
            'placeholder': 'Enter email addresses, one per line'
        }),
        help_text="Enter one email address per line"
    )
    
    message = forms.CharField(
        widget=TinyMCE(attrs={
            'cols': 80,
            'rows': 6,
            'class': 'tinymce-editor'
        }),
        required=False,
        help_text="Optional custom message to include with the invitation"
    )
    
    expires_in_days = forms.IntegerField(
        initial=30,
        min_value=1,
        max_value=365,
        widget=forms.NumberInput(attrs={
            'class': 'my-input'
        }),
        help_text="Number of days until invitation expires"
    )
    
    def clean_emails(self):
        emails_text = self.cleaned_data['emails']
        emails = [email.strip() for email in emails_text.split('\n') if email.strip()]
        
        # Validate each email
        for email in emails:
            try:
                forms.EmailField().clean(email)
            except forms.ValidationError:
                raise forms.ValidationError(f"Invalid email address: {email}")
        
        if not emails:
            raise forms.ValidationError("Please enter at least one email address")
        
        return emails
