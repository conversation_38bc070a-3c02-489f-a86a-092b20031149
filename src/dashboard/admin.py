from django.contrib import admin
from .models import DashboardActivity, SurveyInvitation


@admin.register(DashboardActivity)
class DashboardActivityAdmin(admin.ModelAdmin):
    list_display = ['user', 'activity_type', 'description', 'created_at']
    list_filter = ['activity_type', 'created_at', 'user']
    search_fields = ['user__username', 'description']
    readonly_fields = ['created_at']
    ordering = ['-created_at']


@admin.register(SurveyInvitation)
class SurveyInvitationAdmin(admin.ModelAdmin):
    list_display = ['survey', 'invited_by', 'get_invited_target', 'status', 'created_at', 'expires_at']
    list_filter = ['status', 'created_at', 'expires_at']
    search_fields = ['survey__title', 'invited_by__username', 'invited_user__username', 'email']
    readonly_fields = ['invitation_token', 'created_at', 'sent_at', 'opened_at', 'completed_at']

    def get_invited_target(self, obj):
        return obj.invited_user.username if obj.invited_user else obj.email
    get_invited_target.short_description = 'Invited User/Email'
