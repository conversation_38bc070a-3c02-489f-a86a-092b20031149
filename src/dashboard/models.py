from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from tinymce.models import HTMLField


class DashboardActivity(models.Model):
    """Track user activities in the dashboard."""

    ACTIVITY_TYPES = [
        ('SURVEY_CREATED', 'Survey Created'),
        ('SURVEY_PUBLISHED', 'Survey Published'),
        ('SURVEY_UNPUBLISHED', 'Survey Unpublished'),
        ('QUESTION_ADDED', 'Question Added'),
        ('QUESTION_UPDATED', 'Question Updated'),
        ('QUESTION_DELETED', 'Question Deleted'),
        ('USER_INVITED', 'User Invited'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='dashboard_activities')
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES)
    description = models.TextField()
    related_object_id = models.PositiveIntegerField(null=True, blank=True)
    related_object_type = models.CharField(max_length=50, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = "Dashboard Activities"

    def __str__(self):
        return f"{self.user.username} - {self.get_activity_type_display()}"


class SurveyInvitation(models.Model):
    """Track survey invitations sent to users."""

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('SENT', 'Sent'),
        ('OPENED', 'Opened'),
        ('COMPLETED', 'Completed'),
        ('EXPIRED', 'Expired'),
    ]

    survey = models.ForeignKey('survey.Survey', on_delete=models.CASCADE, related_name='invitations')
    invited_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_invitations')
    invited_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_invitations', null=True, blank=True)
    email = models.EmailField(help_text="Email address if inviting non-registered user")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    invitation_token = models.CharField(max_length=100, unique=True)
    message = models.TextField(blank=True, help_text="Custom invitation message")

    created_at = models.DateTimeField(auto_now_add=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    opened_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField()

    class Meta:
        ordering = ['-created_at']
        unique_together = ['survey', 'invited_user']

    def __str__(self):
        target = self.invited_user.username if self.invited_user else self.email
        return f"{self.survey.title} - {target}"
