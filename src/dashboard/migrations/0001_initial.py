# Generated by Django 5.2.1 on 2025-08-14 09:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('survey', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DashboardActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('SURVEY_CREATED', 'Survey Created'), ('SURVEY_PUBLISHED', 'Survey Published'), ('SURVEY_UNPUBLISHED', 'Survey Unpublished'), ('QUESTION_ADDED', 'Question Added'), ('QUESTION_UPDATED', 'Question Updated'), ('QUESTION_DELETED', 'Question Deleted'), ('USER_INVITED', 'User Invited')], max_length=20)),
                ('description', models.TextField()),
                ('related_object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('related_object_type', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dashboard_activities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Dashboard Activities',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SurveyInvitation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(help_text='Email address if inviting non-registered user', max_length=254)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('SENT', 'Sent'), ('OPENED', 'Opened'), ('COMPLETED', 'Completed'), ('EXPIRED', 'Expired')], default='PENDING', max_length=20)),
                ('invitation_token', models.CharField(max_length=100, unique=True)),
                ('message', models.TextField(blank=True, help_text='Custom invitation message')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('opened_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField()),
                ('invited_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_invitations', to=settings.AUTH_USER_MODEL)),
                ('invited_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='received_invitations', to=settings.AUTH_USER_MODEL)),
                ('survey', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invitations', to='survey.survey')),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('survey', 'invited_user')},
            },
        ),
    ]
