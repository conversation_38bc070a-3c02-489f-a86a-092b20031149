from django.urls import path
from . import views

app_name = 'dashboard'

urlpatterns = [
    # Dashboard home
    path('', views.dashboard_home, name='home'),
    
    # Survey management
    path('surveys/', views.survey_list, name='survey_list'),
    path('surveys/create/', views.survey_create, name='survey_create'),
    path('surveys/<slug:slug>/', views.survey_detail, name='survey_detail'),
    path('surveys/<slug:slug>/toggle-publish/', views.survey_toggle_publish, name='survey_toggle_publish'),
    
    # Question management
    path('surveys/<slug:survey_slug>/questions/create/', views.question_create, name='question_create'),
    path('surveys/<slug:survey_slug>/questions/<int:question_id>/edit/', views.question_edit, name='question_edit'),
    path('surveys/<slug:survey_slug>/questions/<int:question_id>/delete/', views.question_delete, name='question_delete'),
]
