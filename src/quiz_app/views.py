from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, DetailView, CreateView
from django.http import JsonResponse, HttpResponse
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.utils import timezone
from django.db.models import Q, Count, Avg
from django.core.paginator import Paginator
import json
import random

from .models import Category, Quiz, Question, QuizSession, UserAnswer, MCQChoice, UserProgress


class QuizListView(ListView):
    """Display list of published quizzes."""
    model = Quiz
    template_name = 'quiz_app/quiz_list.html'
    context_object_name = 'quizzes'
    paginate_by = 12

    def get_queryset(self):
        queryset = Quiz.objects.filter(is_published=True).select_related('category')

        # Filter by category if specified
        category_slug = self.request.GET.get('category')
        if category_slug:
            queryset = queryset.filter(category__slug=category_slug)

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(description__icontains=search)
            )

        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.annotate(
            quiz_count=Count('quizzes', filter=Q(quizzes__is_published=True))
        ).filter(quiz_count__gt=0)
        context['current_category'] = self.request.GET.get('category', '')
        context['search_query'] = self.request.GET.get('search', '')
        return context


class CategoryListView(ListView):
    """Display list of categories with quiz counts."""
    model = Category
    template_name = 'quiz_app/category_list.html'
    context_object_name = 'categories'

    def get_queryset(self):
        return Category.objects.annotate(
            quiz_count=Count('quizzes', filter=Q(quizzes__is_published=True))
        ).filter(quiz_count__gt=0).order_by('name')


class QuizDetailView(DetailView):
    """Display quiz details and allow starting quiz."""
    model = Quiz
    template_name = 'quiz_app/quiz_detail.html'
    context_object_name = 'quiz'

    def get_queryset(self):
        return Quiz.objects.filter(is_published=True).select_related('category')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        quiz = self.object

        if self.request.user.is_authenticated:
            # Check if user has already attempted this quiz
            try:
                existing_session = QuizSession.objects.get(
                    user=self.request.user,
                    quiz=quiz
                )
                context['existing_session'] = existing_session
                context['can_retake'] = not quiz.single_attempt or not existing_session.is_complete
            except QuizSession.DoesNotExist:
                context['can_retake'] = True

        # Quiz statistics
        context['total_attempts'] = QuizSession.objects.filter(quiz=quiz, is_complete=True).count()
        context['average_score'] = QuizSession.objects.filter(
            quiz=quiz, is_complete=True
        ).aggregate(avg_score=Avg('score'))['avg_score'] or 0

        return context


@login_required
def start_quiz(request, slug):
    """Start a new quiz session."""
    quiz = get_object_or_404(Quiz, slug=slug, is_published=True)

    # Check if user can take this quiz
    if quiz.single_attempt:
        existing_session = QuizSession.objects.filter(
            user=request.user,
            quiz=quiz,
            is_complete=True
        ).first()
        if existing_session:
            messages.error(request, "You have already completed this quiz and cannot retake it.")
            return redirect('quiz_app:quiz_detail', slug=slug)

    # Delete any incomplete sessions for this quiz
    QuizSession.objects.filter(
        user=request.user,
        quiz=quiz,
        is_complete=False
    ).delete()

    # Get questions for this quiz
    questions = list(quiz.questions.filter(is_active=True))

    if not questions:
        messages.error(request, "This quiz has no active questions.")
        return redirect('quiz_app:quiz_detail', slug=slug)

    # Randomize order if needed
    if quiz.random_order:
        random.shuffle(questions)

    # Limit questions if max_questions is set
    if quiz.max_questions and quiz.max_questions < len(questions):
        questions = questions[:quiz.max_questions]

    # Create question order list
    question_order = [q.id for q in questions]
    max_possible_score = sum(q.marks for q in questions)

    # Create new session
    session = QuizSession.objects.create(
        user=request.user,
        quiz=quiz,
        question_order=question_order,
        max_possible_score=max_possible_score
    )

    messages.success(request, f"Quiz '{quiz.title}' started! Good luck!")
    return redirect('quiz_app:take_quiz', session_id=session.id)


@login_required
def take_quiz(request, session_id):
    """Main quiz taking view with HTMX support."""
    session = get_object_or_404(
        QuizSession,
        id=session_id,
        user=request.user,
        is_complete=False
    )

    current_question = session.current_question
    if not current_question:
        # Quiz is complete
        session.complete_session()
        return redirect('quiz_app:quiz_results', session_id=session.id)

    # Handle answer submission
    if request.method == 'POST':
        return handle_answer_submission(request, session, current_question)

    # Get existing answer if any
    existing_answer = UserAnswer.objects.filter(
        session=session,
        question=current_question
    ).first()

    context = {
        'session': session,
        'question': current_question,
        'existing_answer': existing_answer,
        'progress': {
            'current': session.current_question_index + 1,
            'total': len(session.question_order),
            'percentage': session.progress_percentage
        }
    }

    # For HTMX requests, return just the question content
    if request.headers.get('HX-Request'):
        return render(request, 'quiz_app/partials/question_content.html', context)

    return render(request, 'quiz_app/take_quiz.html', context)


def handle_answer_submission(request, session, question):
    """Handle answer submission with HTMX."""
    start_time = timezone.now()

    # Get or create user answer
    user_answer, created = UserAnswer.objects.get_or_create(
        session=session,
        question=question,
        defaults={'answered_at': start_time}
    )

    if question.question_type == 'MCQ':
        choice_id = request.POST.get('choice')
        if choice_id:
            try:
                choice = MCQChoice.objects.get(id=choice_id, question=question)
                user_answer.selected_choice = choice
                user_answer.is_correct = choice.is_correct
                user_answer.marks_awarded = question.marks if choice.is_correct else 0
            except MCQChoice.DoesNotExist:
                messages.error(request, "Invalid choice selected.")
                return redirect('quiz_app:take_quiz', session_id=session.id)

    elif question.question_type == 'SUBJECTIVE':
        text_answer = request.POST.get('text_answer', '').strip()
        user_answer.text_answer = text_answer
        # Subjective answers need manual grading
        user_answer.is_correct = None
        user_answer.marks_awarded = 0

    user_answer.save()

    # Update session score
    session.score += user_answer.marks_awarded
    session.current_question_index += 1
    session.save()

    # Check if quiz is complete
    if session.current_question_index >= len(session.question_order):
        session.complete_session()

        # Update user progress
        progress, created = UserProgress.objects.get_or_create(user=session.user)
        progress.update_stats(session)

        if request.headers.get('HX-Request'):
            return HttpResponse(
                headers={'HX-Redirect': reverse('quiz_app:quiz_results', kwargs={'session_id': session.id})}
            )
        return redirect('quiz_app:quiz_results', session_id=session.id)

    # Continue to next question
    if request.headers.get('HX-Request'):
        next_question = session.current_question
        context = {
            'session': session,
            'question': next_question,
            'existing_answer': None,
            'progress': {
                'current': session.current_question_index + 1,
                'total': len(session.question_order),
                'percentage': session.progress_percentage
            }
        }
        return render(request, 'quiz_app/partials/question_content.html', context)

    return redirect('quiz_app:take_quiz', session_id=session.id)


@login_required
def quiz_results(request, session_id):
    """Display quiz results."""
    session = get_object_or_404(
        QuizSession,
        id=session_id,
        user=request.user,
        is_complete=True
    )

    # Get all answers for detailed review
    answers = UserAnswer.objects.filter(session=session).select_related(
        'question', 'selected_choice'
    ).order_by('answered_at')

    context = {
        'session': session,
        'answers': answers,
        'quiz': session.quiz,
        'passed': session.passed,
        'score_percentage': session.score_percentage,
    }

    return render(request, 'quiz_app/quiz_results.html', context)
