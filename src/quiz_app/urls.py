from django.urls import path
from . import views

app_name = 'quiz_app'

urlpatterns = [
    # Quiz browsing
    path('', views.QuizListView.as_view(), name='quiz_list'),
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('quiz/<slug:slug>/', views.QuizDetailView.as_view(), name='quiz_detail'),

    # Quiz taking
    path('quiz/<slug:slug>/start/', views.start_quiz, name='start_quiz'),
    path('session/<uuid:session_id>/', views.take_quiz, name='take_quiz'),
    path('session/<uuid:session_id>/results/', views.quiz_results, name='quiz_results'),
]
