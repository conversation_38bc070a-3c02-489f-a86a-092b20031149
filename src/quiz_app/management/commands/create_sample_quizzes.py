from django.core.management.base import BaseCommand
from django.utils.text import slugify
from quiz_app.models import Category, Quiz, Question, MCQChoice


class Command(BaseCommand):
    help = 'Create sample quiz data for testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample quiz data...')

        # Create categories
        categories_data = [
            {
                'name': 'Python Programming',
                'description': 'Test your Python programming knowledge'
            },
            {
                'name': 'Web Development',
                'description': 'HTML, CSS, JavaScript and web technologies'
            },
            {
                'name': 'Django Framework',
                'description': 'Django web framework concepts and best practices'
            },
            {
                'name': 'General Knowledge',
                'description': 'Miscellaneous topics and trivia'
            }
        ]

        categories = {}
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={
                    'description': cat_data['description'],
                    'slug': slugify(cat_data['name'])
                }
            )
            categories[cat_data['name']] = category
            if created:
                self.stdout.write(f'Created category: {category.name}')

        # Create quizzes
        quizzes_data = [
            {
                'title': 'Python Basics Quiz',
                'description': 'Test your understanding of Python fundamentals including variables, data types, and basic operations.',
                'category': 'Python Programming',
                'time_limit': 15,
                'pass_mark': 70,
            },
            {
                'title': 'Web Development Fundamentals',
                'description': 'Assess your knowledge of HTML, CSS, and basic web development concepts.',
                'category': 'Web Development',
                'time_limit': 20,
                'pass_mark': 60,
            },
            {
                'title': 'Django Models and Views',
                'description': 'Test your understanding of Django models, views, and URL patterns.',
                'category': 'Django Framework',
                'time_limit': 25,
                'pass_mark': 75,
            }
        ]

        quizzes = {}
        for quiz_data in quizzes_data:
            quiz, created = Quiz.objects.get_or_create(
                title=quiz_data['title'],
                defaults={
                    'description': quiz_data['description'],
                    'category': categories[quiz_data['category']],
                    'slug': slugify(quiz_data['title']),
                    'time_limit': quiz_data['time_limit'],
                    'pass_mark': quiz_data['pass_mark'],
                    'is_published': True,
                    'random_order': True,
                    'show_answers_at_end': True,
                    'show_correct_answers': True,
                }
            )
            quizzes[quiz_data['title']] = quiz
            if created:
                self.stdout.write(f'Created quiz: {quiz.title}')

        # Create questions and choices
        questions_data = [
            # Python Basics Quiz
            {
                'quiz': 'Python Basics Quiz',
                'content': 'What is the correct way to create a list in Python?',
                'explanation': 'Lists in Python are created using square brackets [].',
                'choices': [
                    {'content': 'my_list = []', 'is_correct': True},
                    {'content': 'my_list = ()', 'is_correct': False},
                    {'content': 'my_list = {}', 'is_correct': False},
                    {'content': 'my_list = <>', 'is_correct': False},
                ]
            },
            {
                'quiz': 'Python Basics Quiz',
                'content': 'Which of the following is a mutable data type in Python?',
                'explanation': 'Lists are mutable, meaning they can be changed after creation.',
                'choices': [
                    {'content': 'tuple', 'is_correct': False},
                    {'content': 'string', 'is_correct': False},
                    {'content': 'list', 'is_correct': True},
                    {'content': 'int', 'is_correct': False},
                ]
            },
            {
                'quiz': 'Python Basics Quiz',
                'content': 'What does the len() function return?',
                'explanation': 'The len() function returns the number of items in an object.',
                'choices': [
                    {'content': 'The length of an object', 'is_correct': True},
                    {'content': 'The type of an object', 'is_correct': False},
                    {'content': 'The value of an object', 'is_correct': False},
                    {'content': 'The memory address', 'is_correct': False},
                ]
            },
            # Web Development Quiz
            {
                'quiz': 'Web Development Fundamentals',
                'content': 'Which HTML tag is used to create a hyperlink?',
                'explanation': 'The <a> tag defines a hyperlink, which is used to link from one page to another.',
                'choices': [
                    {'content': '<link>', 'is_correct': False},
                    {'content': '<a>', 'is_correct': True},
                    {'content': '<href>', 'is_correct': False},
                    {'content': '<url>', 'is_correct': False},
                ]
            },
            {
                'quiz': 'Web Development Fundamentals',
                'content': 'What does CSS stand for?',
                'explanation': 'CSS stands for Cascading Style Sheets, used for styling web pages.',
                'choices': [
                    {'content': 'Computer Style Sheets', 'is_correct': False},
                    {'content': 'Creative Style Sheets', 'is_correct': False},
                    {'content': 'Cascading Style Sheets', 'is_correct': True},
                    {'content': 'Colorful Style Sheets', 'is_correct': False},
                ]
            },
            # Django Quiz
            {
                'quiz': 'Django Models and Views',
                'content': 'What is the purpose of Django models?',
                'explanation': 'Django models define the structure of your database tables and provide an API for database operations.',
                'choices': [
                    {'content': 'To handle HTTP requests', 'is_correct': False},
                    {'content': 'To define database structure', 'is_correct': True},
                    {'content': 'To render templates', 'is_correct': False},
                    {'content': 'To manage static files', 'is_correct': False},
                ]
            },
            {
                'quiz': 'Django Models and Views',
                'content': 'Which Django command creates database tables?',
                'explanation': 'The migrate command applies database migrations and creates/updates tables.',
                'choices': [
                    {'content': 'makemigrations', 'is_correct': False},
                    {'content': 'migrate', 'is_correct': True},
                    {'content': 'syncdb', 'is_correct': False},
                    {'content': 'createdb', 'is_correct': False},
                ]
            },
        ]

        for q_data in questions_data:
            quiz = quizzes[q_data['quiz']]
            question, created = Question.objects.get_or_create(
                content=q_data['content'],
                defaults={
                    'question_type': 'MCQ',
                    'category': quiz.category,
                    'explanation': q_data['explanation'],
                    'marks': 1,
                }
            )
            
            if created:
                question.quiz.add(quiz)
                self.stdout.write(f'Created question: {question.content[:50]}...')
                
                # Create choices
                for choice_data in q_data['choices']:
                    MCQChoice.objects.create(
                        question=question,
                        content=choice_data['content'],
                        is_correct=choice_data['is_correct']
                    )

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample quiz data!')
        )
