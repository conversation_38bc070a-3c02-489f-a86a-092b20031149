from django.contrib import admin
from django.utils.html import format_html
from .models import Category, Quiz, Question, MCQ<PERSON>hoice, QuizSession, UserAnswer, UserProgress


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'quiz_count', 'created_at']
    prepopulated_fields = {'slug': ('name',)}
    search_fields = ['name', 'description']

    def quiz_count(self, obj):
        return obj.quizzes.count()
    quiz_count.short_description = 'Quizzes'


class MCQChoiceInline(admin.TabularInline):
    model = MCQChoice
    extra = 4
    fields = ['content', 'is_correct', 'order']


@admin.register(Question)
class QuestionAdmin(admin.ModelAdmin):
    list_display = ['content_preview', 'question_type', 'category', 'marks', 'is_active']
    list_filter = ['question_type', 'category', 'is_active', 'created_at']
    search_fields = ['content', 'explanation']
    filter_horizontal = ['quiz']
    inlines = [MCQChoiceInline]

    def content_preview(self, obj):
        return obj.content[:50] + "..." if len(obj.content) > 50 else obj.content
    content_preview.short_description = 'Content'

    def get_inlines(self, request, obj):
        if obj and obj.question_type == 'MCQ':
            return [MCQChoiceInline]
        return []


@admin.register(Quiz)
class QuizAdmin(admin.ModelAdmin):
    list_display = ['title', 'category', 'question_count', 'is_published', 'created_at']
    list_filter = ['category', 'is_published', 'single_attempt', 'created_at']
    search_fields = ['title', 'description']
    prepopulated_fields = {'slug': ('title',)}
    # Note: questions is a reverse M2M, so we can't use filter_horizontal

    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug', 'description', 'category')
        }),
        ('Quiz Settings', {
            'fields': ('random_order', 'max_questions', 'time_limit', 'single_attempt')
        }),
        ('Display Settings', {
            'fields': ('show_answers_at_end', 'show_correct_answers')
        }),
        ('Scoring', {
            'fields': ('pass_mark', 'success_text', 'fail_text')
        }),
        ('Status', {
            'fields': ('is_published',)
        }),
    )

    def question_count(self, obj):
        return obj.questions.count()
    question_count.short_description = 'Questions'


@admin.register(QuizSession)
class QuizSessionAdmin(admin.ModelAdmin):
    list_display = ['user', 'quiz', 'score_display', 'progress_display', 'status', 'started_at']
    list_filter = ['is_complete', 'passed', 'quiz__category', 'started_at']
    search_fields = ['user__username', 'quiz__title']
    readonly_fields = ['id', 'started_at', 'completed_at', 'time_spent', 'score_percentage']

    def score_display(self, obj):
        return f"{obj.score}/{obj.max_possible_score} ({obj.score_percentage:.1f}%)"
    score_display.short_description = 'Score'

    def progress_display(self, obj):
        return f"{obj.current_question_index}/{len(obj.question_order)}"
    progress_display.short_description = 'Progress'

    def status(self, obj):
        if obj.is_complete:
            color = 'green' if obj.passed else 'red'
            status = 'Passed' if obj.passed else 'Failed'
            return format_html(
                '<span style="color: {};">{}</span>',
                color, status
            )
        return format_html('<span style="color: orange;">In Progress</span>')
    status.short_description = 'Status'


@admin.register(UserAnswer)
class UserAnswerAdmin(admin.ModelAdmin):
    list_display = ['user', 'question_preview', 'answer_preview', 'is_correct', 'marks_awarded']
    list_filter = ['is_correct', 'question__question_type', 'answered_at']
    search_fields = ['session__user__username', 'question__content']

    def user(self, obj):
        return obj.session.user.username
    user.short_description = 'User'

    def question_preview(self, obj):
        return obj.question.content[:30] + "..."
    question_preview.short_description = 'Question'

    def answer_preview(self, obj):
        if obj.selected_choice:
            return obj.selected_choice.content[:30] + "..."
        elif obj.text_answer:
            return obj.text_answer[:30] + "..."
        return "No answer"
    answer_preview.short_description = 'Answer'


@admin.register(UserProgress)
class UserProgressAdmin(admin.ModelAdmin):
    list_display = ['user', 'total_quizzes_attempted', 'pass_rate_display', 'accuracy_display', 'current_streak']
    search_fields = ['user__username']
    readonly_fields = ['accuracy_percentage', 'pass_rate']

    def pass_rate_display(self, obj):
        return f"{obj.pass_rate:.1f}%"
    pass_rate_display.short_description = 'Pass Rate'

    def accuracy_display(self, obj):
        return f"{obj.accuracy_percentage:.1f}%"
    accuracy_display.short_description = 'Accuracy'
