from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
import json
import uuid


class Category(models.Model):
    """Quiz categories for organizing quizzes by topic."""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    slug = models.SlugField(unique=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['name']

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('quiz_app:category_detail', kwargs={'slug': self.slug})


class Quiz(models.Model):
    """Main quiz model containing questions and settings."""
    title = models.CharField(max_length=200)
    description = models.TextField()
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='quizzes')
    slug = models.SlugField(unique=True)

    # Quiz settings
    random_order = models.BooleanField(default=False, help_text="Randomize question order")
    max_questions = models.PositiveIntegerField(
        null=True, blank=True,
        help_text="Limit number of questions (leave blank for all)"
    )
    time_limit = models.PositiveIntegerField(
        null=True, blank=True,
        help_text="Time limit in minutes (leave blank for no limit)"
    )

    # Attempt settings
    single_attempt = models.BooleanField(default=False)
    show_answers_at_end = models.BooleanField(default=True)
    show_correct_answers = models.BooleanField(default=True)

    # Scoring
    pass_mark = models.PositiveIntegerField(
        default=50,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Percentage required to pass"
    )

    # Messages
    success_text = models.TextField(
        default="Congratulations! You passed the quiz.",
        help_text="Message shown when user passes"
    )
    fail_text = models.TextField(
        default="Sorry, you didn't pass this time. Try again!",
        help_text="Message shown when user fails"
    )

    # Status
    is_published = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('quiz_app:quiz_detail', kwargs={'slug': self.slug})

    @property
    def total_questions(self):
        return self.questions.count()

    @property
    def max_score(self):
        return sum(q.marks for q in self.questions.all())


class QuestionManager(models.Manager):
    """Custom manager for Question model."""

    def published(self):
        return self.filter(is_active=True)

    def mcq(self):
        return self.filter(question_type='MCQ')

    def subjective(self):
        return self.filter(question_type='SUBJECTIVE')


class Question(models.Model):
    """Base question model for all question types."""

    QUESTION_TYPES = [
        ('MCQ', 'Multiple Choice'),
        ('SUBJECTIVE', 'Subjective/Essay'),
    ]

    quiz = models.ManyToManyField(Quiz, related_name='questions')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='questions')

    question_type = models.CharField(max_length=20, choices=QUESTION_TYPES)
    content = models.TextField(help_text="The question text")
    explanation = models.TextField(
        blank=True,
        help_text="Explanation shown after answering"
    )

    # Media
    image = models.ImageField(upload_to='questions/', blank=True, null=True)
    external_link = models.URLField(blank=True, help_text="Optional external reference link")

    # Scoring
    marks = models.PositiveIntegerField(default=1)

    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = QuestionManager()

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"{self.get_question_type_display()}: {self.content[:50]}..."

    def get_absolute_url(self):
        return reverse('quiz_app:question_detail', kwargs={'pk': self.pk})


class MCQChoice(models.Model):
    """Multiple choice answer options."""
    question = models.ForeignKey(
        Question,
        on_delete=models.CASCADE,
        related_name='choices',
        limit_choices_to={'question_type': 'MCQ'}
    )
    content = models.CharField(max_length=500)
    is_correct = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order', 'id']

    def __str__(self):
        return f"{self.content} ({'Correct' if self.is_correct else 'Incorrect'})"


class QuizSessionManager(models.Manager):
    """Custom manager for QuizSession model."""

    def active(self):
        return self.filter(is_complete=False)

    def completed(self):
        return self.filter(is_complete=True)

    def for_user(self, user):
        return self.filter(user=user)


class QuizSession(models.Model):
    """Tracks a user's progress through a quiz."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='quiz_sessions')
    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE, related_name='sessions')

    # Session data
    question_order = models.JSONField(default=list, help_text="List of question IDs in order")
    current_question_index = models.PositiveIntegerField(default=0)

    # Scoring
    score = models.PositiveIntegerField(default=0)
    max_possible_score = models.PositiveIntegerField(default=0)

    # Timing
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    time_spent = models.DurationField(null=True, blank=True)

    # Status
    is_complete = models.BooleanField(default=False)
    passed = models.BooleanField(null=True, blank=True)

    objects = QuizSessionManager()

    class Meta:
        ordering = ['-started_at']
        unique_together = ['user', 'quiz']  # One session per user per quiz if single_attempt

    def __str__(self):
        return f"{self.user.username} - {self.quiz.title} ({self.get_status()})"

    def get_status(self):
        if self.is_complete:
            return "Completed"
        return "In Progress"

    @property
    def current_question(self):
        if self.current_question_index < len(self.question_order):
            question_id = self.question_order[self.current_question_index]
            return Question.objects.get(id=question_id)
        return None

    @property
    def progress_percentage(self):
        if not self.question_order:
            return 0
        return (self.current_question_index / len(self.question_order)) * 100

    @property
    def score_percentage(self):
        if self.max_possible_score == 0:
            return 0
        return (self.score / self.max_possible_score) * 100

    def complete_session(self):
        """Mark session as complete and calculate final results."""
        self.is_complete = True
        self.completed_at = timezone.now()
        self.time_spent = self.completed_at - self.started_at
        self.passed = self.score_percentage >= self.quiz.pass_mark
        self.save()

    def get_absolute_url(self):
        return reverse('quiz_app:session_detail', kwargs={'pk': self.pk})


class UserAnswer(models.Model):
    """Stores user answers to questions."""
    session = models.ForeignKey(QuizSession, on_delete=models.CASCADE, related_name='answers')
    question = models.ForeignKey(Question, on_delete=models.CASCADE)

    # MCQ answer
    selected_choice = models.ForeignKey(
        MCQChoice,
        on_delete=models.CASCADE,
        null=True, blank=True,
        help_text="Selected choice for MCQ questions"
    )

    # Subjective answer
    text_answer = models.TextField(
        blank=True,
        help_text="Text answer for subjective questions"
    )

    # Scoring
    is_correct = models.BooleanField(null=True, blank=True)
    marks_awarded = models.PositiveIntegerField(default=0)

    # Timing
    answered_at = models.DateTimeField(auto_now_add=True)
    time_taken = models.DurationField(null=True, blank=True)

    class Meta:
        unique_together = ['session', 'question']
        ordering = ['answered_at']

    def __str__(self):
        return f"{self.session.user.username} - {self.question.content[:30]}..."

    def save(self, *args, **kwargs):
        """Auto-calculate correctness for MCQ questions."""
        if self.question.question_type == 'MCQ' and self.selected_choice:
            self.is_correct = self.selected_choice.is_correct
            self.marks_awarded = self.question.marks if self.is_correct else 0
        super().save(*args, **kwargs)


class UserProgress(models.Model):
    """Tracks overall user progress and statistics."""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='quiz_progress')

    # Statistics
    total_quizzes_attempted = models.PositiveIntegerField(default=0)
    total_quizzes_passed = models.PositiveIntegerField(default=0)
    total_questions_answered = models.PositiveIntegerField(default=0)
    total_correct_answers = models.PositiveIntegerField(default=0)

    # Achievements
    highest_score = models.PositiveIntegerField(default=0)
    longest_streak = models.PositiveIntegerField(default=0)
    current_streak = models.PositiveIntegerField(default=0)

    # Timing
    total_time_spent = models.DurationField(default=timezone.timedelta)
    last_activity = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - Progress"

    @property
    def accuracy_percentage(self):
        if self.total_questions_answered == 0:
            return 0
        return (self.total_correct_answers / self.total_questions_answered) * 100

    @property
    def pass_rate(self):
        if self.total_quizzes_attempted == 0:
            return 0
        return (self.total_quizzes_passed / self.total_quizzes_attempted) * 100

    def update_stats(self, session):
        """Update progress stats after completing a quiz session."""
        self.total_quizzes_attempted += 1
        if session.passed:
            self.total_quizzes_passed += 1
            self.current_streak += 1
            self.longest_streak = max(self.longest_streak, self.current_streak)
        else:
            self.current_streak = 0

        # Update question stats
        correct_answers = session.answers.filter(is_correct=True).count()
        self.total_questions_answered += session.answers.count()
        self.total_correct_answers += correct_answers

        # Update timing
        if session.time_spent:
            self.total_time_spent += session.time_spent

        # Update highest score
        if session.score_percentage > self.highest_score:
            self.highest_score = int(session.score_percentage)

        self.save()


# Signal to create UserProgress when User is created
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=User)
def create_user_progress(sender, instance, created, **kwargs):
    if created:
        UserProgress.objects.create(user=instance)
