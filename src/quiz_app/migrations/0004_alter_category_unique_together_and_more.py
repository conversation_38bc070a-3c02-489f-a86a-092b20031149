# Generated by Django 5.2.1 on 2025-08-14 05:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("quiz_app", "0003_alter_question_question_type"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="category",
            unique_together=set(),
        ),
        migrations.AlterUniqueTogether(
            name="quiz",
            unique_together=set(),
        ),
        migrations.RemoveField(
            model_name="question",
            name="created_by",
        ),
        migrations.AlterField(
            model_name="category",
            name="name",
            field=models.CharField(max_length=100, unique=True),
        ),
        migrations.AlterField(
            model_name="category",
            name="slug",
            field=models.SlugField(unique=True),
        ),
        migrations.AlterField(
            model_name="question",
            name="question_type",
            field=models.CharField(
                choices=[
                    ("MCQ", "Multiple Choice"),
                    ("SUBJECTIVE", "Subjective/Essay"),
                ],
                max_length=20,
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="quiz",
            name="slug",
            field=models.Slug<PERSON>ield(unique=True),
        ),
        migrations.RemoveField(
            model_name="category",
            name="created_by",
        ),
        migrations.RemoveField(
            model_name="quiz",
            name="created_by",
        ),
    ]
