# Generated by Django 5.2.1 on 2025-08-08 04:11

import datetime
import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('slug', models.SlugField(unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_type', models.CharField(choices=[('MCQ', 'Multiple Choice'), ('SUBJECTIVE', 'Subjective/Essay')], max_length=20)),
                ('content', models.TextField(help_text='The question text')),
                ('explanation', models.TextField(blank=True, help_text='Explanation shown after answering')),
                ('image', models.ImageField(blank=True, null=True, upload_to='questions/')),
                ('external_link', models.URLField(blank=True, help_text='Optional external reference link')),
                ('marks', models.PositiveIntegerField(default=1)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='quiz_app.category')),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='MCQChoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.CharField(max_length=500)),
                ('is_correct', models.BooleanField(default=False)),
                ('order', models.PositiveIntegerField(default=0)),
                ('question', models.ForeignKey(limit_choices_to={'question_type': 'MCQ'}, on_delete=django.db.models.deletion.CASCADE, related_name='choices', to='quiz_app.question')),
            ],
            options={
                'ordering': ['order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='Quiz',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('slug', models.SlugField(unique=True)),
                ('random_order', models.BooleanField(default=False, help_text='Randomize question order')),
                ('max_questions', models.PositiveIntegerField(blank=True, help_text='Limit number of questions (leave blank for all)', null=True)),
                ('time_limit', models.PositiveIntegerField(blank=True, help_text='Time limit in minutes (leave blank for no limit)', null=True)),
                ('single_attempt', models.BooleanField(default=False)),
                ('show_answers_at_end', models.BooleanField(default=True)),
                ('show_correct_answers', models.BooleanField(default=True)),
                ('pass_mark', models.PositiveIntegerField(default=50, help_text='Percentage required to pass', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('success_text', models.TextField(default='Congratulations! You passed the quiz.', help_text='Message shown when user passes')),
                ('fail_text', models.TextField(default="Sorry, you didn't pass this time. Try again!", help_text='Message shown when user fails')),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quizzes', to='quiz_app.category')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='question',
            name='quiz',
            field=models.ManyToManyField(related_name='questions', to='quiz_app.quiz'),
        ),
        migrations.CreateModel(
            name='QuizSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('question_order', models.JSONField(default=list, help_text='List of question IDs in order')),
                ('current_question_index', models.PositiveIntegerField(default=0)),
                ('score', models.PositiveIntegerField(default=0)),
                ('max_possible_score', models.PositiveIntegerField(default=0)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('time_spent', models.DurationField(blank=True, null=True)),
                ('is_complete', models.BooleanField(default=False)),
                ('passed', models.BooleanField(blank=True, null=True)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='quiz_app.quiz')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quiz_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-started_at'],
                'unique_together': {('user', 'quiz')},
            },
        ),
        migrations.CreateModel(
            name='UserProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_quizzes_attempted', models.PositiveIntegerField(default=0)),
                ('total_quizzes_passed', models.PositiveIntegerField(default=0)),
                ('total_questions_answered', models.PositiveIntegerField(default=0)),
                ('total_correct_answers', models.PositiveIntegerField(default=0)),
                ('highest_score', models.PositiveIntegerField(default=0)),
                ('longest_streak', models.PositiveIntegerField(default=0)),
                ('current_streak', models.PositiveIntegerField(default=0)),
                ('total_time_spent', models.DurationField(default=datetime.timedelta)),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='quiz_progress', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text_answer', models.TextField(blank=True, help_text='Text answer for subjective questions')),
                ('is_correct', models.BooleanField(blank=True, null=True)),
                ('marks_awarded', models.PositiveIntegerField(default=0)),
                ('answered_at', models.DateTimeField(auto_now_add=True)),
                ('time_taken', models.DurationField(blank=True, null=True)),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='quiz_app.question')),
                ('selected_choice', models.ForeignKey(blank=True, help_text='Selected choice for MCQ questions', null=True, on_delete=django.db.models.deletion.CASCADE, to='quiz_app.mcqchoice')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='answers', to='quiz_app.quizsession')),
            ],
            options={
                'ordering': ['answered_at'],
                'unique_together': {('session', 'question')},
            },
        ),
    ]
