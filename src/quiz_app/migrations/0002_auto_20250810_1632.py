# Generated by Django 5.2.1 on 2025-08-10 06:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


def create_default_user_and_assign_ownership(apps, schema_editor):
    """Create a default user and assign ownership of existing data."""
    User = apps.get_model('auth', 'User')
    Category = apps.get_model('quiz_app', 'Category')
    Quiz = apps.get_model('quiz_app', 'Quiz')
    Question = apps.get_model('quiz_app', 'Question')

    # Create a default admin user if no users exist
    if not User.objects.exists():
        admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True,
            is_superuser=True
        )
    else:
        # Use the first existing user
        admin_user = User.objects.first()

    # Assign ownership to existing categories
    Category.objects.filter(created_by__isnull=True).update(created_by=admin_user)

    # Assign ownership to existing quizzes
    Quiz.objects.filter(created_by__isnull=True).update(created_by=admin_user)

    # Assign ownership to existing questions
    Question.objects.filter(created_by__isnull=True).update(created_by=admin_user)


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('quiz_app', '0001_initial'),
    ]

    operations = [
        # Add created_by fields as nullable first
        migrations.AddField(
            model_name='category',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='created_categories', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='quiz',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='created_quizzes', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='question',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='created_questions', to=settings.AUTH_USER_MODEL),
        ),

        # Run data migration to assign ownership
        migrations.RunPython(create_default_user_and_assign_ownership),

        # Make created_by fields non-nullable
        migrations.AlterField(
            model_name='category',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_categories', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='quiz',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_quizzes', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='question',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_questions', to=settings.AUTH_USER_MODEL),
        ),

        # Update unique constraints
        migrations.AlterUniqueTogether(
            name='category',
            unique_together={('name', 'created_by')},
        ),
        migrations.AlterUniqueTogether(
            name='quiz',
            unique_together={('slug', 'created_by')},
        ),

        # Remove old unique constraints
        migrations.AlterField(
            model_name='category',
            name='name',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='category',
            name='slug',
            field=models.SlugField(),
        ),
        migrations.AlterField(
            model_name='quiz',
            name='slug',
            field=models.SlugField(),
        ),
    ]
