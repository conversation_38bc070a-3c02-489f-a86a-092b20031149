# Generated by Django 5.2.1 on 2025-08-10 05:54

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('quiz_app', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='QuizAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_invitations_sent', models.PositiveIntegerField(default=0)),
                ('total_participants', models.PositiveIntegerField(default=0)),
                ('total_completions', models.PositiveIntegerField(default=0)),
                ('average_score', models.FloatField(default=0.0)),
                ('highest_score', models.FloatField(default=0.0)),
                ('lowest_score', models.FloatField(default=0.0)),
                ('average_completion_time', models.DurationField(blank=True, null=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('easiest_question', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='analytics_easiest', to='quiz_app.question')),
                ('most_difficult_question', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='analytics_most_difficult', to='quiz_app.question')),
                ('quiz', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='quiz_app.quiz')),
            ],
        ),
        migrations.CreateModel(
            name='QuizInvitation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(help_text='Email address for invitation', max_length=254)),
                ('message', models.TextField(blank=True, help_text='Personal message with invitation')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('ACCEPTED', 'Accepted'), ('DECLINED', 'Declined'), ('EXPIRED', 'Expired')], default='PENDING', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(help_text='When invitation expires')),
                ('responded_at', models.DateTimeField(blank=True, null=True)),
                ('invited_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_invitations', to=settings.AUTH_USER_MODEL)),
                ('invited_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='received_invitations', to=settings.AUTH_USER_MODEL)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invitations', to='quiz_app.quiz')),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('quiz', 'email')},
            },
        ),
    ]
