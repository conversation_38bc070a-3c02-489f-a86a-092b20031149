# Generated by Django 5.2.1 on 2025-08-10 06:59

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('quiz_admin', '0001_initial'),
        ('quiz_app', '0002_auto_20250810_1632'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CategoryManager',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('can_create_quizzes', models.BooleanField(default=True, help_text='Can create new quizzes in this category')),
                ('can_edit_quizzes', models.BooleanField(default=True, help_text='Can edit existing quizzes in this category')),
                ('can_invite_participants', models.BooleanField(default=True, help_text='Can send quiz invitations')),
                ('can_view_analytics', models.BooleanField(default=True, help_text='Can view quiz analytics')),
                ('can_manage_questions', models.<PERSON>olean<PERSON>ield(default=False, help_text='Can create/edit questions')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('ACCEPTED', 'Accepted'), ('DECLINED', 'Declined'), ('REVOKED', 'Revoked')], default='PENDING', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('responded_at', models.DateTimeField(blank=True, null=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='managers', to='quiz_app.category')),
                ('invited_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='category_manager_invitations', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='managed_categories', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('category', 'user')},
            },
        ),
        migrations.CreateModel(
            name='QuizManager',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('can_edit_quiz', models.BooleanField(default=True, help_text='Can edit quiz settings')),
                ('can_invite_participants', models.BooleanField(default=True, help_text='Can send quiz invitations')),
                ('can_view_analytics', models.BooleanField(default=True, help_text='Can view quiz analytics')),
                ('can_manage_questions', models.BooleanField(default=False, help_text='Can create/edit questions for this quiz')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('ACCEPTED', 'Accepted'), ('DECLINED', 'Declined'), ('REVOKED', 'Revoked')], default='PENDING', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('responded_at', models.DateTimeField(blank=True, null=True)),
                ('invited_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quiz_manager_invitations', to=settings.AUTH_USER_MODEL)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='managers', to='quiz_app.quiz')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='managed_quizzes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('quiz', 'user')},
            },
        ),
    ]
