from django.core.management.base import BaseCommand
from django.utils.text import slugify
from survey.models import Survey, Question, RadioChoice


class Command(BaseCommand):
    help = 'Create a sample survey for testing'

    def handle(self, *args, **options):
        # Create sample survey
        survey, created = Survey.objects.get_or_create(
            slug='sample-survey',
            defaults={
                'title': 'Sample Survey',
                'description': 'A sample survey to test radio buttons and text inputs.',
                'is_published': True
            }
        )

        if created:
            self.stdout.write(f'Created survey: {survey.title}')
        else:
            self.stdout.write(f'Survey already exists: {survey.title}')

        # Create radio button question
        radio_question, created = Question.objects.get_or_create(
            survey=survey,
            question_type='RADIO',
            content='What is your preferred communication method?',
            defaults={
                'order': 1,
                'is_required': True
            }
        )

        if created:
            # Add radio choices
            choices = [
                'Email',
                'Phone call',
                'Video conference',
                'In-person meeting',
                'Text message'
            ]
            
            for i, choice_text in enumerate(choices):
                RadioChoice.objects.create(
                    question=radio_question,
                    content=choice_text,
                    order=i + 1
                )
            
            self.stdout.write(f'Created radio question with {len(choices)} choices')

        # Create text input question
        text_question, created = Question.objects.get_or_create(
            survey=survey,
            question_type='TEXT',
            content='Please describe your experience with our services and any suggestions for improvement.',
            defaults={
                'order': 2,
                'is_required': True
            }
        )

        if created:
            self.stdout.write('Created text input question')

        # Create another radio question
        radio_question2, created = Question.objects.get_or_create(
            survey=survey,
            question_type='RADIO',
            content='How would you rate your overall satisfaction?',
            defaults={
                'order': 3,
                'is_required': True
            }
        )

        if created:
            # Add rating choices
            choices = [
                'Very Satisfied',
                'Satisfied',
                'Neutral',
                'Dissatisfied',
                'Very Dissatisfied'
            ]
            
            for i, choice_text in enumerate(choices):
                RadioChoice.objects.create(
                    question=radio_question2,
                    content=choice_text,
                    order=i + 1
                )
            
            self.stdout.write(f'Created second radio question with {len(choices)} choices')

        self.stdout.write(
            self.style.SUCCESS(
                f'Sample survey setup complete! Visit /survey/{survey.slug}/ to test it.'
            )
        )
