from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
import uuid


class Survey(models.Model):
    """Main survey model containing questions and settings."""
    title = models.CharField(max_length=200)
    description = models.TextField()
    slug = models.SlugField(unique=True)

    # Survey settings
    is_published = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('survey:survey_detail', kwargs={'slug': self.slug})

    @property
    def total_questions(self):
        return self.questions.count()


class Question(models.Model):
    """Survey question model for different question types."""

    QUESTION_TYPES = [
        ('RADIO', 'Radio Button (Select One)'),
        ('TEXT', 'Multiline Text Input'),
    ]

    survey = models.ForeignKey(Survey, on_delete=models.CASCADE, related_name='questions')
    question_type = models.CharField(max_length=20, choices=QUESTION_TYPES)
    content = models.TextField(help_text="The question text")
    order = models.PositiveIntegerField(default=0)
    is_required = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'created_at']

    def __str__(self):
        return f"{self.get_question_type_display()}: {self.content[:50]}..."


class RadioChoice(models.Model):
    """Radio button choice options for single-select questions."""
    question = models.ForeignKey(
        Question,
        on_delete=models.CASCADE,
        related_name='choices',
        limit_choices_to={'question_type': 'RADIO'}
    )
    content = models.CharField(max_length=500)
    order = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['order', 'id']

    def __str__(self):
        return self.content


class SurveyResponse(models.Model):
    """Tracks a user's response to a survey."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='survey_responses')
    survey = models.ForeignKey(Survey, on_delete=models.CASCADE, related_name='responses')

    # Status
    is_complete = models.BooleanField(default=False)
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-started_at']
        unique_together = ['user', 'survey']

    def __str__(self):
        return f"{self.user.username} - {self.survey.title}"

    def complete_response(self):
        """Mark response as complete."""
        self.is_complete = True
        self.completed_at = timezone.now()
        self.save()


class Answer(models.Model):
    """Stores user answers to survey questions."""
    response = models.ForeignKey(SurveyResponse, on_delete=models.CASCADE, related_name='answers')
    question = models.ForeignKey(Question, on_delete=models.CASCADE)

    # Radio answer
    selected_choice = models.ForeignKey(
        RadioChoice,
        on_delete=models.CASCADE,
        null=True, blank=True,
        help_text="Selected choice for radio questions"
    )

    # Text answer
    text_answer = models.TextField(
        blank=True,
        help_text="Text answer for multiline questions"
    )

    answered_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['response', 'question']
        ordering = ['answered_at']

    def __str__(self):
        return f"{self.response.user.username} - {self.question.content[:30]}..."
