from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from .models import Survey, Question, SurveyResponse, Answer, RadioChoice


def survey_list(request):
    """Display list of published surveys."""
    surveys = Survey.objects.filter(is_published=True)
    return render(request, 'survey/survey_list.html', {
        'surveys': surveys
    })


def survey_detail(request, slug):
    """Display survey details and start button."""
    survey = get_object_or_404(Survey, slug=slug, is_published=True)

    # Check if user already has a response
    user_response = None
    if request.user.is_authenticated:
        try:
            user_response = SurveyResponse.objects.get(user=request.user, survey=survey)
        except SurveyResponse.DoesNotExist:
            pass

    return render(request, 'survey/survey_detail.html', {
        'survey': survey,
        'user_response': user_response
    })


@login_required
def take_survey(request, slug):
    """Take a survey - display questions and handle responses."""
    survey = get_object_or_404(Survey, slug=slug, is_published=True)

    # Get or create survey response
    response, created = SurveyResponse.objects.get_or_create(
        user=request.user,
        survey=survey
    )

    if response.is_complete:
        return redirect('survey:survey_complete', slug=slug)

    # Get existing answers and attach them to questions
    existing_answers = {}
    for answer in response.answers.all():
        if answer.selected_choice:
            existing_answers[answer.question.id] = answer.selected_choice.id
        else:
            existing_answers[answer.question.id] = answer.text_answer

    questions = survey.questions.all()

    # Attach existing answers to questions for template use
    for question in questions:
        question.existing_answer = existing_answers.get(question.id, '')
        if question.question_type == 'RADIO':
            question.existing_choice_id = existing_answers.get(question.id, None)

    if request.method == 'POST':
        # Process form submission
        all_answered = True

        for question in questions:
            answer_key = f'question_{question.id}'

            if question.question_type == 'RADIO':
                choice_id = request.POST.get(answer_key)
                if choice_id:
                    try:
                        choice = RadioChoice.objects.get(id=choice_id, question=question)
                        Answer.objects.update_or_create(
                            response=response,
                            question=question,
                            defaults={'selected_choice': choice}
                        )
                    except RadioChoice.DoesNotExist:
                        pass
                elif question.is_required:
                    all_answered = False

            elif question.question_type == 'TEXT':
                text_answer = request.POST.get(answer_key, '').strip()
                if text_answer:
                    Answer.objects.update_or_create(
                        response=response,
                        question=question,
                        defaults={'text_answer': text_answer}
                    )
                elif question.is_required:
                    all_answered = False

        if all_answered:
            response.complete_response()
            messages.success(request, 'Survey completed successfully!')
            return redirect('survey:survey_complete', slug=slug)
        else:
            messages.error(request, 'Please answer all required questions.')

    return render(request, 'survey/take_survey.html', {
        'survey': survey,
        'response': response,
        'questions': questions,
    })


@login_required
def survey_complete(request, slug):
    """Display survey completion page."""
    survey = get_object_or_404(Survey, slug=slug, is_published=True)
    response = get_object_or_404(SurveyResponse, user=request.user, survey=survey)

    return render(request, 'survey/survey_complete.html', {
        'survey': survey,
        'response': response
    })
