from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from .models import Survey, Question, SurveyResponse, Answer, RadioChoice


def survey_list(request):
    """Display list of published surveys."""
    surveys = Survey.objects.filter(is_published=True)
    return render(request, 'survey/survey_list.html', {
        'surveys': surveys
    })


def survey_detail(request, slug):
    """Display survey details and start button."""
    survey = get_object_or_404(Survey, slug=slug, is_published=True)

    # Check if user already has a response
    user_response = None
    if request.user.is_authenticated:
        try:
            user_response = SurveyResponse.objects.get(user=request.user, survey=survey)
        except SurveyResponse.DoesNotExist:
            pass

    return render(request, 'survey/survey_detail.html', {
        'survey': survey,
        'user_response': user_response
    })


@login_required
def take_survey(request, slug):
    """Take a survey - display one question at a time."""
    survey = get_object_or_404(Survey, slug=slug, is_published=True)

    # Get or create survey response
    response, created = SurveyResponse.objects.get_or_create(
        user=request.user,
        survey=survey
    )

    if response.is_complete:
        return redirect('survey:survey_complete', slug=slug)

    # Get current question index from session or start at 0
    current_index = request.session.get(f'survey_{survey.id}_index', 0)
    questions = list(survey.questions.all())

    # If we've gone past the last question, complete the survey
    if current_index >= len(questions):
        response.complete_response()
        messages.success(request, 'Survey completed successfully!')
        # Clear session data
        if f'survey_{survey.id}_index' in request.session:
            del request.session[f'survey_{survey.id}_index']
        return redirect('survey:survey_complete', slug=slug)

    current_question = questions[current_index]

    # Calculate progress
    progress = {
        'current': current_index + 1,
        'total': len(questions),
        'percentage': ((current_index + 1) / len(questions)) * 100
    }

    return render(request, 'survey/take_survey.html', {
        'survey': survey,
        'response': response,
        'current_question': current_question,
        'progress': progress,
    })


@login_required
def survey_question(request, slug):
    """Handle individual question submission and return next question."""
    survey = get_object_or_404(Survey, slug=slug, is_published=True)
    response = get_object_or_404(SurveyResponse, user=request.user, survey=survey)

    if response.is_complete:
        return redirect('survey:survey_complete', slug=slug)

    current_index = request.session.get(f'survey_{survey.id}_index', 0)
    questions = list(survey.questions.all())

    if current_index >= len(questions):
        return redirect('survey:survey_complete', slug=slug)

    current_question = questions[current_index]

    if request.method == 'POST':
        # Process the current question answer
        answer_saved = False

        if current_question.question_type == 'RADIO':
            choice_id = request.POST.get('answer')
            if choice_id:
                try:
                    choice = RadioChoice.objects.get(id=choice_id, question=current_question)
                    Answer.objects.update_or_create(
                        response=response,
                        question=current_question,
                        defaults={'selected_choice': choice}
                    )
                    answer_saved = True
                except RadioChoice.DoesNotExist:
                    pass

        elif current_question.question_type == 'TEXT':
            text_answer = request.POST.get('answer', '').strip()
            if text_answer:
                Answer.objects.update_or_create(
                    response=response,
                    question=current_question,
                    defaults={'text_answer': text_answer}
                )
                answer_saved = True

        # If required question not answered, show error
        if current_question.is_required and not answer_saved:
            messages.error(request, 'This question is required.')
            # Return current question with error
            existing_answer = get_existing_answer(response, current_question)
            progress = {
                'current': current_index + 1,
                'total': len(questions),
                'percentage': ((current_index + 1) / len(questions)) * 100
            }
            return render(request, 'survey/partials/question_content.html', {
                'survey': survey,
                'response': response,
                'question': current_question,
                'existing_answer': existing_answer,
                'progress': progress,
            })

        # Move to next question
        current_index += 1
        request.session[f'survey_{survey.id}_index'] = current_index

        # Check if we've completed all questions
        if current_index >= len(questions):
            response.complete_response()
            # Clear session data
            if f'survey_{survey.id}_index' in request.session:
                del request.session[f'survey_{survey.id}_index']
            return render(request, 'survey/partials/survey_complete_content.html', {
                'survey': survey,
                'response': response,
            })

        # Get next question
        next_question = questions[current_index]
        existing_answer = get_existing_answer(response, next_question)

        progress = {
            'current': current_index + 1,
            'total': len(questions),
            'percentage': ((current_index + 1) / len(questions)) * 100
        }

        return render(request, 'survey/partials/question_content.html', {
            'survey': survey,
            'response': response,
            'question': next_question,
            'existing_answer': existing_answer,
            'progress': progress,
        })

    # GET request - return current question
    existing_answer = get_existing_answer(response, current_question)
    progress = {
        'current': current_index + 1,
        'total': len(questions),
        'percentage': ((current_index + 1) / len(questions)) * 100
    }

    return render(request, 'survey/partials/question_content.html', {
        'survey': survey,
        'response': response,
        'question': current_question,
        'existing_answer': existing_answer,
        'progress': progress,
    })


def get_existing_answer(response, question):
    """Helper function to get existing answer for a question."""
    try:
        answer = Answer.objects.get(response=response, question=question)
        if answer.selected_choice:
            return answer.selected_choice.id
        else:
            return answer.text_answer
    except Answer.DoesNotExist:
        return None


@login_required
def survey_complete(request, slug):
    """Display survey completion page."""
    survey = get_object_or_404(Survey, slug=slug, is_published=True)
    response = get_object_or_404(SurveyResponse, user=request.user, survey=survey)

    return render(request, 'survey/survey_complete.html', {
        'survey': survey,
        'response': response
    })
