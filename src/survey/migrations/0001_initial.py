# Generated by Django 5.2.1 on 2025-08-14 00:16

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Survey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('survey_type', models.CharField(choices=[('FACE_TO_FACE', 'Face-to-Face Benchmark'), ('PHONE', 'Phone Benchmark'), ('ZOOM', 'Zoom Benchmark'), ('CUSTOM', 'Custom Survey')], default='CUSTOM', max_length=20)),
                ('slug', models.SlugField()),
                ('random_order', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Randomize question order')),
                ('allow_anonymous', models.BooleanField(default=False, help_text='Allow anonymous responses')),
                ('multiple_responses', models.BooleanField(default=False, help_text='Allow multiple responses per user')),
                ('enable_scoring', models.BooleanField(default=True, help_text='Enable automatic scoring')),
                ('scoring_rubric', models.JSONField(blank=True, default=dict, help_text='Scoring rules and thresholds')),
                ('enable_ai_analysis', models.BooleanField(default=True, help_text='Enable AI-powered analysis')),
                ('ai_analysis_prompt', models.TextField(blank=True, help_text='Custom prompt for AI analysis (leave blank for default)')),
                ('is_published', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_surveys', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SurveyAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_responses', models.PositiveIntegerField(default=0)),
                ('completed_responses', models.PositiveIntegerField(default=0)),
                ('anonymous_responses', models.PositiveIntegerField(default=0)),
                ('average_score', models.FloatField(default=0.0)),
                ('median_score', models.FloatField(default=0.0)),
                ('highest_score', models.FloatField(default=0.0)),
                ('lowest_score', models.FloatField(default=0.0)),
                ('score_distribution', models.JSONField(blank=True, default=dict)),
                ('average_completion_time', models.DurationField(blank=True, null=True)),
                ('median_completion_time', models.DurationField(blank=True, null=True)),
                ('question_analytics', models.JSONField(blank=True, default=dict)),
                ('ai_insights', models.JSONField(blank=True, default=dict)),
                ('ai_summary', models.TextField(blank=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('survey', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='survey.survey')),
            ],
            options={
                'verbose_name_plural': 'Survey Analytics',
            },
        ),
        migrations.CreateModel(
            name='SurveyCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('slug', models.SlugField(unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_survey_categories', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name_plural': 'Survey Categories',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='survey',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='surveys', to='survey.surveycategory'),
        ),
        migrations.CreateModel(
            name='SurveyQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_text', models.TextField(help_text='The question text')),
                ('question_type', models.CharField(choices=[('LIKERT_5', 'Likert Scale (1-5)'), ('LIKERT_7', 'Likert Scale (1-7)'), ('RATING_10', 'Rating Scale (1-10)'), ('MULTIPLE_CHOICE', 'Multiple Choice'), ('YES_NO', 'Yes/No'), ('TEXT_SHORT', 'Short Text Response'), ('TEXT_LONG', 'Long Text Response'), ('SCALE_CUSTOM', 'Custom Scale')], max_length=20)),
                ('order', models.PositiveIntegerField(default=0)),
                ('is_required', models.BooleanField(default=True)),
                ('help_text', models.TextField(blank=True, help_text='Additional guidance for respondents')),
                ('weight', models.FloatField(default=1.0, help_text='Weight for scoring calculations')),
                ('scoring_config', models.JSONField(blank=True, default=dict, help_text='Question-specific scoring rules')),
                ('scale_min', models.IntegerField(blank=True, help_text='Minimum scale value', null=True)),
                ('scale_max', models.IntegerField(blank=True, help_text='Maximum scale value', null=True)),
                ('scale_labels', models.JSONField(blank=True, default=dict, help_text='Labels for scale points')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('survey', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='survey.survey')),
            ],
            options={
                'ordering': ['order', 'created_at'],
                'unique_together': {('survey', 'order')},
            },
        ),
        migrations.CreateModel(
            name='SurveyQuestionChoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.CharField(max_length=500)),
                ('value', models.IntegerField(help_text='Numeric value for scoring')),
                ('order', models.PositiveIntegerField(default=0)),
                ('question', models.ForeignKey(limit_choices_to={'question_type': 'MULTIPLE_CHOICE'}, on_delete=django.db.models.deletion.CASCADE, related_name='choices', to='survey.surveyquestion')),
            ],
            options={
                'ordering': ['order', 'id'],
                'unique_together': {('question', 'order')},
            },
        ),
        migrations.CreateModel(
            name='SurveySession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_key', models.CharField(blank=True, help_text='Session key for anonymous users', max_length=100)),
                ('question_order', models.JSONField(default=list, help_text='List of question IDs in order')),
                ('current_question_index', models.PositiveIntegerField(default=0)),
                ('total_score', models.FloatField(blank=True, null=True)),
                ('max_possible_score', models.FloatField(blank=True, null=True)),
                ('score_breakdown', models.JSONField(blank=True, default=dict, help_text='Detailed scoring by category')),
                ('ai_analysis_results', models.JSONField(blank=True, default=dict, help_text='AI-generated insights')),
                ('ai_analysis_completed', models.BooleanField(default=False)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('time_spent', models.DurationField(blank=True, null=True)),
                ('is_complete', models.BooleanField(default=False)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('survey', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='survey.survey')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='survey_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='survey',
            unique_together={('slug', 'created_by')},
        ),
        migrations.CreateModel(
            name='SurveyResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('numeric_value', models.FloatField(blank=True, help_text='Numeric response (scales, ratings)', null=True)),
                ('text_value', models.TextField(blank=True, help_text='Text response')),
                ('calculated_score', models.FloatField(blank=True, null=True)),
                ('responded_at', models.DateTimeField(auto_now_add=True)),
                ('time_taken', models.DurationField(blank=True, null=True)),
                ('choice_value', models.ForeignKey(blank=True, help_text='Selected choice for multiple choice questions', null=True, on_delete=django.db.models.deletion.CASCADE, to='survey.surveyquestionchoice')),
                ('question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='survey.surveyquestion')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='survey.surveysession')),
            ],
            options={
                'ordering': ['responded_at'],
                'unique_together': {('session', 'question')},
            },
        ),
    ]
