from django.contrib import admin
from .models import Survey, Question, RadioChoice, SurveyResponse, Answer


class RadioChoiceInline(admin.TabularInline):
    model = RadioChoice
    extra = 3
    fields = ['content', 'order']


@admin.register(Question)
class QuestionAdmin(admin.ModelAdmin):
    list_display = ['content', 'question_type', 'survey', 'order', 'is_required']
    list_filter = ['question_type', 'survey', 'is_required']
    search_fields = ['content']
    inlines = [RadioChoiceInline]

    def get_inlines(self, request, obj):
        if obj and obj.question_type == 'RADIO':
            return [RadioChoiceInline]
        return []


@admin.register(Survey)
class SurveyAdmin(admin.ModelAdmin):
    list_display = ['title', 'is_published', 'total_questions', 'created_at']
    list_filter = ['is_published', 'created_at']
    search_fields = ['title', 'description']
    prepopulated_fields = {'slug': ('title',)}


class AnswerInline(admin.TabularInline):
    model = Answer
    extra = 0
    readonly_fields = ['question', 'selected_choice', 'text_answer', 'answered_at']


@admin.register(SurveyResponse)
class SurveyResponseAdmin(admin.ModelAdmin):
    list_display = ['user', 'survey', 'is_complete', 'started_at', 'completed_at']
    list_filter = ['is_complete', 'survey', 'started_at']
    search_fields = ['user__username', 'survey__title']
    readonly_fields = ['id', 'started_at', 'completed_at']
    inlines = [AnswerInline]


@admin.register(RadioChoice)
class RadioChoiceAdmin(admin.ModelAdmin):
    list_display = ['content', 'question', 'order']
    list_filter = ['question__survey']
    search_fields = ['content', 'question__content']


@admin.register(Answer)
class AnswerAdmin(admin.ModelAdmin):
    list_display = ['response', 'question', 'selected_choice', 'answered_at']
    list_filter = ['question__question_type', 'answered_at']
    search_fields = ['response__user__username', 'question__content']
    readonly_fields = ['answered_at']
