# Generated by Django 5.2.1 on 2025-08-11 02:27

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('quiz_app', '0003_alter_question_question_type'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BulkQuestionImport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('method', models.CharField(choices=[('TEXT', 'Text Parsing'), ('CSV', 'CSV Upload'), ('MODAL', 'Single Question Modal')], max_length=20)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed')], default='PENDING', max_length=20)),
                ('total_questions', models.PositiveIntegerField(default=0)),
                ('successful_imports', models.PositiveIntegerField(default=0)),
                ('failed_imports', models.PositiveIntegerField(default=0)),
                ('source_file', models.FileField(blank=True, null=True, upload_to='question_imports/')),
                ('error_log', models.JSONField(blank=True, default=list, help_text='List of import errors')),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('imported_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='question_imports', to=settings.AUTH_USER_MODEL)),
                ('quiz', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='question_imports', to='quiz_app.quiz')),
            ],
            options={
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='QuestionTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Template name for organization', max_length=200)),
                ('description', models.TextField(blank=True, help_text='Description of this question template')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_public', models.BooleanField(default=False, help_text='Allow other users to use this template')),
                ('tags', models.JSONField(blank=True, default=list, help_text='Tags for organizing templates')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='question_templates', to='quiz_app.category')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='question_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='QuestionBank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('is_public', models.BooleanField(default=False, help_text='Allow other users to use questions from this bank')),
                ('tags', models.JSONField(blank=True, default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='question_banks', to='quiz_app.category')),
                ('collaborators', models.ManyToManyField(blank=True, related_name='shared_question_banks', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='question_banks', to=settings.AUTH_USER_MODEL)),
                ('questions', models.ManyToManyField(blank=True, related_name='question_banks', to='quiz_app.question')),
            ],
            options={
                'ordering': ['-updated_at'],
                'unique_together': {('name', 'created_by')},
            },
        ),
    ]
