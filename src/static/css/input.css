@import url("https://fonts.googleapis.com/css2?family=Arsenal+SC:ital,wght@0,400;0,700;1,400;1,700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100..900;1,100..900&family=Raleway:ital,wght@0,100..900;1,100..900&display=swap");
@import "tailwindcss";
@plugin "@tailwindcss/typography";

/*@plugin "daisyui" {*/
/*   themes: bumblebee --default, dim --prefersdark, cupcake, dracula;*/

/* }*/
@plugin "daisyui";


@plugin "daisyui/theme" {
  name: "light";
  default: true;
  prefersdark: false;
  color-scheme: "light";
  --color-base-100: oklch(98% 0.016 73.684);
  --color-base-200: oklch(95% 0.038 75.164);
  --color-base-300: oklch(90% 0.076 70.697);
  --color-base-content: oklch(40% 0.123 38.172);
  --color-primary: oklch(0% 0 0);
  --color-primary-content: oklch(100% 0 0);
  --color-secondary: oklch(22.45% 0.075 37.85);
  --color-secondary-content: oklch(90% 0.076 70.697);
  --color-accent: oklch(46.44% 0.111 37.85);
  --color-accent-content: oklch(90% 0.076 70.697);
  --color-neutral: oklch(55% 0.195 38.402);
  --color-neutral-content: oklch(98% 0.016 73.684);
  --color-info: oklch(42% 0.199 265.638);
  --color-info-content: oklch(90% 0.076 70.697);
  --color-success: oklch(0.609 0.105 184.023);
  --color-success-content: oklch(90% 0.076 70.697);
  --color-warning: oklch(82% 0.189 84.429);
  --color-warning-content: oklch(41% 0.112 45.904);
  --color-error: oklch(70% 0.191 22.216);
  --color-error-content: oklch(39% 0.141 25.723);
  --radius-selector: 0.25rem;
  --radius-field: 0.25rem;
  --radius-box: 0.25rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}


@plugin "daisyui/theme" {
  name: "dark";
  default: false;
  prefersdark: false;
  color-scheme: "dark";
  --color-base-100: oklch(20.768% 0.039 265.754);
  --color-base-200: oklch(19.314% 0.037 265.754);
  --color-base-300: oklch(17.86% 0.034 265.754);
  --color-base-content: oklch(84.153% 0.007 265.754);
  --color-primary: oklch(75.351% 0.138 232.661);
  --color-primary-content: oklch(15.07% 0.027 232.661);
  --color-secondary: oklch(68.011% 0.158 276.934);
  --color-secondary-content: oklch(13.602% 0.031 276.934);
  --color-accent: oklch(72.36% 0.176 350.048);
  --color-accent-content: oklch(14.472% 0.035 350.048);
  --color-neutral: oklch(27.949% 0.036 260.03);
  --color-neutral-content: oklch(85.589% 0.007 260.03);
  --color-info: oklch(68.455% 0.148 237.251);
  --color-info-content: oklch(0% 0 0);
  --color-success: oklch(78.452% 0.132 181.911);
  --color-success-content: oklch(15.69% 0.026 181.911);
  --color-warning: oklch(83.242% 0.139 82.95);
  --color-warning-content: oklch(16.648% 0.027 82.95);
  --color-error: oklch(71.785% 0.17 13.118);
  --color-error-content: oklch(14.357% 0.034 13.118);
  --radius-selector: 0.25rem;
  --radius-field: 0.25rem;
  --radius-box: 0.25rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;

}



@theme {
    --font-roboto: "Roboto", sans-serif;
    --font-arsenal: "Arsenal SC", sans-serif;
    --font-poppins: "Poppins", sans-serif;
    --font-raleway: "Raleway", sans-serif;
    --font-awesome: "Font Awesome 6 Pro", sans-serif;
}


/*my custom text styles*/
/*my custom text styles*/
.my-h1 {
    @apply text-lg md:text-xl font-medium font-raleway mb-2;
}
.my-h2 {
    @apply font-roboto text-base md:text-lg font-medium;
}
.my-h3 {
    @apply font-roboto text-sm md:text-base font-medium;
}
.my-p {
    @apply font-poppins text-xs md:text-sm;
}

/*default input*/
.my-input {
    @apply w-full border-1 border-accent-content rounded-sm p-6 outline-none focus:outline-none focus:ring-0 focus:border-accent-content pr-10;
}
/*default card*/
.my-card {
	@apply bg-base-100 border border-base-300 rounded-sm p-4 md:p-6 shadow-sm space-y-4 md:space-y-6;
}