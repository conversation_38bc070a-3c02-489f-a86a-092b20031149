/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@import url("https://fonts.googleapis.com/css2?family=Arsenal+SC:ital,wght@0,400;0,700;1,400;1,700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100..900;1,100..900&family=Raleway:ital,wght@0,100..900;1,100..900&display=swap");
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, <PERSON><PERSON><PERSON>, "Liberation Mono",
      "Courier New", monospace;
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-orange-500: oklch(70.5% 0.213 47.604);
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-sm: 0.25rem;
    --radius-lg: 0.5rem;
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --font-roboto: "Roboto", sans-serif;
    --font-arsenal: "Arsenal SC", sans-serif;
    --font-poppins: "Poppins", sans-serif;
    --font-raleway: "Raleway", sans-serif;
    --font-awesome: "Font Awesome 6 Pro", sans-serif;
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .modal {
    pointer-events: none;
    visibility: hidden;
    position: fixed;
    inset: calc(0.25rem * 0);
    margin: calc(0.25rem * 0);
    display: grid;
    height: 100%;
    max-height: none;
    width: 100%;
    max-width: none;
    align-items: center;
    justify-items: center;
    background-color: transparent;
    padding: calc(0.25rem * 0);
    color: inherit;
    overflow-x: hidden;
    transition: translate 0.3s ease-out, visibility 0.3s allow-discrete, background-color 0.3s ease-out, opacity 0.1s ease-out;
    overflow-y: hidden;
    overscroll-behavior: contain;
    z-index: 999;
    &::backdrop {
      display: none;
    }
    &.modal-open, &[open], &:target {
      pointer-events: auto;
      visibility: visible;
      opacity: 100%;
      background-color: oklch(0% 0 0/ 0.4);
      .modal-box {
        translate: 0 0;
        scale: 1;
        opacity: 1;
      }
    }
    @starting-style {
      &.modal-open, &[open], &:target {
        visibility: hidden;
        opacity: 0%;
      }
    }
  }
  .drawer-side {
    pointer-events: none;
    visibility: hidden;
    position: fixed;
    inset-inline-start: calc(0.25rem * 0);
    top: calc(0.25rem * 0);
    z-index: 1;
    grid-column-start: 1;
    grid-row-start: 1;
    display: grid;
    width: 100%;
    grid-template-columns: repeat(1, minmax(0, 1fr));
    grid-template-rows: repeat(1, minmax(0, 1fr));
    align-items: flex-start;
    justify-items: start;
    overflow-x: hidden;
    overflow-y: hidden;
    overscroll-behavior: contain;
    opacity: 0%;
    transition: opacity 0.2s ease-out 0.1s allow-discrete, visibility 0.3s ease-out 0.1s allow-discrete;
    height: 100vh;
    height: 100dvh;
    > .drawer-overlay {
      position: sticky;
      top: calc(0.25rem * 0);
      cursor: pointer;
      place-self: stretch;
      background-color: oklch(0% 0 0 / 40%);
    }
    > * {
      grid-column-start: 1;
      grid-row-start: 1;
    }
    > *:not(.drawer-overlay) {
      will-change: transform;
      transition: translate 0.3s ease-out;
      translate: -100%;
      [dir="rtl"] & {
        translate: 100%;
      }
    }
  }
  .drawer-open {
    > .drawer-side {
      overflow-y: auto;
    }
    > .drawer-toggle {
      display: none;
      & ~ .drawer-side {
        pointer-events: auto;
        visibility: visible;
        position: sticky;
        display: block;
        width: auto;
        overscroll-behavior: auto;
        opacity: 100%;
        & > .drawer-overlay {
          cursor: default;
          background-color: transparent;
        }
        & > *:not(.drawer-overlay) {
          translate: 0%;
          [dir="rtl"] & {
            translate: 0%;
          }
        }
      }
      &:checked ~ .drawer-side {
        pointer-events: auto;
        visibility: visible;
      }
    }
  }
  .drawer-toggle {
    position: fixed;
    height: calc(0.25rem * 0);
    width: calc(0.25rem * 0);
    appearance: none;
    opacity: 0%;
    &:checked {
      & ~ .drawer-side {
        pointer-events: auto;
        visibility: visible;
        overflow-y: auto;
        opacity: 100%;
        & > *:not(.drawer-overlay) {
          translate: 0%;
        }
      }
    }
    &:focus-visible ~ .drawer-content label.drawer-button {
      outline: 2px solid;
      outline-offset: 2px;
    }
  }
  .menu {
    display: flex;
    width: fit-content;
    flex-direction: column;
    flex-wrap: wrap;
    padding: calc(0.25rem * 2);
    --menu-active-fg: var(--color-neutral-content);
    --menu-active-bg: var(--color-neutral);
    font-size: 0.875rem;
    :where(li ul) {
      position: relative;
      margin-inline-start: calc(0.25rem * 4);
      padding-inline-start: calc(0.25rem * 2);
      white-space: nowrap;
      &:before {
        position: absolute;
        inset-inline-start: calc(0.25rem * 0);
        top: calc(0.25rem * 3);
        bottom: calc(0.25rem * 3);
        background-color: var(--color-base-content);
        opacity: 10%;
        width: var(--border);
        content: "";
      }
    }
    :where(li > .menu-dropdown:not(.menu-dropdown-show)) {
      display: none;
    }
    :where(li:not(.menu-title) > *:not(ul, details, .menu-title, .btn)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
      display: grid;
      grid-auto-flow: column;
      align-content: flex-start;
      align-items: center;
      gap: calc(0.25rem * 2);
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 3);
      padding-block: calc(0.25rem * 1.5);
      text-align: start;
      transition-property: color, background-color, box-shadow;
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
      grid-auto-columns: minmax(auto, max-content) auto max-content;
      text-wrap: balance;
      user-select: none;
    }
    :where(li > details > summary) {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
      &::-webkit-details-marker {
        display: none;
      }
    }
    :where(li > details > summary), :where(li > .menu-dropdown-toggle) {
      &:after {
        justify-self: flex-end;
        display: block;
        height: 0.375rem;
        width: 0.375rem;
        rotate: -135deg;
        translate: 0 -1px;
        transition-property: rotate, translate;
        transition-duration: 0.2s;
        content: "";
        transform-origin: 50% 50%;
        box-shadow: 2px 2px inset;
        pointer-events: none;
      }
    }
    :where(li > details[open] > summary):after, :where(li > .menu-dropdown-toggle.menu-dropdown-show):after {
      rotate: 45deg;
      translate: 0 1px;
    }
    :where( li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title), li:not(.menu-title, .disabled) > details > summary:not(.menu-title) ):not(.menu-active, :active, .btn) {
      &.menu-focus, &:focus-visible {
        cursor: pointer;
        background-color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
        }
        color: var(--color-base-content);
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    :where( li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title):not(.menu-active, :active, .btn):hover, li:not(.menu-title, .disabled) > details > summary:not(.menu-title):not(.menu-active, :active, .btn):hover ) {
      cursor: pointer;
      background-color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
      }
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
      box-shadow: 0 1px oklch(0% 0 0 / 0.01) inset, 0 -1px oklch(100% 0 0 / 0.01) inset;
    }
    :where(li:empty) {
      background-color: var(--color-base-content);
      opacity: 10%;
      margin: 0.5rem 1rem;
      height: 1px;
    }
    :where(li) {
      position: relative;
      display: flex;
      flex-shrink: 0;
      flex-direction: column;
      flex-wrap: wrap;
      align-items: stretch;
      .badge {
        justify-self: flex-end;
      }
      & > *:not(ul, .menu-title, details, .btn):active, & > *:not(ul, .menu-title, details, .btn).menu-active, & > details > summary:active {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
        color: var(--menu-active-fg);
        background-color: var(--menu-active-bg);
        background-size: auto, calc(var(--noise) * 100%);
        background-image: none, var(--fx-noise);
        &:not(&:active) {
          box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);
        }
      }
      &.menu-disabled {
        pointer-events: none;
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
    }
    .dropdown:focus-within {
      .menu-dropdown-toggle:after {
        rotate: 45deg;
        translate: 0 1px;
      }
    }
    .dropdown-content {
      margin-top: calc(0.25rem * 2);
      padding: calc(0.25rem * 2);
      &:before {
        display: none;
      }
    }
  }
  .collapse-arrow {
    > .collapse-title:after {
      position: absolute;
      display: block;
      height: 0.5rem;
      width: 0.5rem;
      transform: translateY(-100%) rotate(45deg);
      transition-property: all;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      transition-duration: 0.2s;
      top: 1.9rem;
      inset-inline-end: 1.4rem;
      content: "";
      transform-origin: 75% 75%;
      box-shadow: 2px 2px;
      pointer-events: none;
    }
  }
  .collapse-plus {
    > .collapse-title:after {
      position: absolute;
      display: block;
      height: 0.5rem;
      width: 0.5rem;
      transition-property: all;
      transition-duration: 300ms;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
      top: 0.9rem;
      inset-inline-end: 1.4rem;
      content: "+";
      pointer-events: none;
    }
  }
  .dropdown {
    position: relative;
    display: inline-block;
    position-area: var(--anchor-v, bottom) var(--anchor-h, span-right);
    & > *:not(summary):focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    .dropdown-content {
      position: absolute;
    }
    &:not(details, .dropdown-open, .dropdown-hover:hover, :focus-within) {
      .dropdown-content {
        display: none;
        transform-origin: top;
        opacity: 0%;
        scale: 95%;
      }
    }
    &[popover], .dropdown-content {
      z-index: 999;
      animation: dropdown 0.2s;
      transition-property: opacity, scale, display;
      transition-behavior: allow-discrete;
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
    @starting-style {
      &[popover], .dropdown-content {
        scale: 95%;
        opacity: 0;
      }
    }
    &.dropdown-open, &:not(.dropdown-hover):focus, &:focus-within {
      > [tabindex]:first-child {
        pointer-events: none;
      }
      .dropdown-content {
        opacity: 100%;
      }
    }
    &.dropdown-hover:hover {
      .dropdown-content {
        opacity: 100%;
        scale: 100%;
      }
    }
    &:is(details) {
      summary {
        &::-webkit-details-marker {
          display: none;
        }
      }
    }
    &.dropdown-open, &:focus, &:focus-within {
      .dropdown-content {
        scale: 100%;
      }
    }
    &:where([popover]) {
      background: #0000;
    }
    &[popover] {
      position: fixed;
      color: inherit;
      @supports not (position-area: bottom) {
        margin: auto;
        &.dropdown-open:not(:popover-open) {
          display: none;
          transform-origin: top;
          opacity: 0%;
          scale: 95%;
        }
        &::backdrop {
          background-color: color-mix(in oklab, #000 30%, #0000);
        }
      }
      &:not(.dropdown-open, :popover-open) {
        display: none;
        transform-origin: top;
        opacity: 0%;
        scale: 95%;
      }
    }
  }
  .btn {
    :where(&) {
      width: unset;
    }
    display: inline-flex;
    flex-shrink: 0;
    cursor: pointer;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 1.5);
    text-align: center;
    vertical-align: middle;
    outline-offset: 2px;
    webkit-user-select: none;
    user-select: none;
    padding-inline: var(--btn-p);
    color: var(--btn-fg);
    --tw-prose-links: var(--btn-fg);
    height: var(--size);
    font-size: var(--fontsize, 0.875rem);
    font-weight: 600;
    outline-color: var(--btn-color, var(--color-base-content));
    transition-property: color, background-color, border-color, box-shadow;
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    transition-duration: 0.2s;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    background-color: var(--btn-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--btn-noise);
    border-width: var(--border);
    border-style: solid;
    border-color: var(--btn-border);
    text-shadow: 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 0.15));
    touch-action: manipulation;
    box-shadow: 0 0.5px 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow);
    --size: calc(var(--size-field, 0.25rem) * 10);
    --btn-bg: var(--btn-color, var(--color-base-200));
    --btn-fg: var(--color-base-content);
    --btn-p: 1rem;
    --btn-border: var(--btn-bg);
    @supports (color: color-mix(in lab, red, red)) {
      --btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));
    }
    --btn-shadow: 0 3px 2px -2px var(--btn-bg),
    0 4px 3px -2px var(--btn-bg);
    @supports (color: color-mix(in lab, red, red)) {
      --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000),
    0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000);
    }
    --btn-noise: var(--fx-noise);
    .prose & {
      text-decoration-line: none;
    }
    @media (hover: hover) {
      &:hover {
        --btn-bg: var(--btn-color, var(--color-base-200));
        @supports (color: color-mix(in lab, red, red)) {
          --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
        }
      }
    }
    &:focus-visible {
      outline-width: 2px;
      outline-style: solid;
      isolation: isolate;
    }
    &:active:not(.btn-active) {
      translate: 0 0.5px;
      --btn-bg: var(--btn-color, var(--color-base-200));
      @supports (color: color-mix(in lab, red, red)) {
        --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);
      }
      --btn-border: var(--btn-color, var(--color-base-200));
      @supports (color: color-mix(in lab, red, red)) {
        --btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
      }
      --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);
    }
    &:is(:disabled, [disabled], .btn-disabled) {
      &:not(.btn-link, .btn-ghost) {
        background-color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
        }
        box-shadow: none;
      }
      pointer-events: none;
      --btn-border: #0000;
      --btn-noise: none;
      --btn-fg: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
      }
      @media (hover: hover) {
        &:hover {
          pointer-events: none;
          background-color: var(--color-neutral);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
          }
          --btn-border: #0000;
          --btn-fg: var(--color-base-content);
          @supports (color: color-mix(in lab, red, red)) {
            --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
          }
        }
      }
    }
    &:is(input[type="checkbox"], input[type="radio"]) {
      appearance: none;
      &::after {
        content: attr(aria-label);
      }
    }
    &:where(input:checked:not(.filter .btn)) {
      --btn-color: var(--color-primary);
      --btn-fg: var(--color-primary-content);
      isolation: isolate;
    }
  }
  .loading {
    pointer-events: none;
    display: inline-block;
    aspect-ratio: 1 / 1;
    background-color: currentColor;
    vertical-align: middle;
    width: calc(var(--size-selector, 0.25rem) * 6);
    mask-size: 100%;
    mask-repeat: no-repeat;
    mask-position: center;
    mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
  }
  .collapse {
    &:not(td, tr, colgroup) {
      visibility: visible;
    }
    position: relative;
    display: grid;
    overflow: hidden;
    border-radius: var(--radius-box, 1rem);
    width: 100%;
    grid-template-rows: max-content 0fr;
    transition: grid-template-rows 0.2s;
    isolation: isolate;
    > input:is([type="checkbox"], [type="radio"]) {
      grid-column-start: 1;
      grid-row-start: 1;
      appearance: none;
      opacity: 0;
      z-index: 1;
      width: 100%;
      padding: 1rem;
      padding-inline-end: 3rem;
      min-height: 1lh;
      transition: background-color 0.2s ease-out;
    }
    &:is([open], :focus:not(.collapse-close)), &:not(.collapse-close):has(> input:is([type="checkbox"], [type="radio"]):checked) {
      grid-template-rows: max-content 1fr;
    }
    &:is([open], :focus:not(.collapse-close)) > .collapse-content, &:not(.collapse-close) > :where(input:is([type="checkbox"], [type="radio"]):checked ~ .collapse-content) {
      visibility: visible;
      min-height: fit-content;
    }
    &:focus-visible, &:has(> input:is([type="checkbox"], [type="radio"]):focus-visible) {
      outline-color: var(--color-base-content);
      outline-style: solid;
      outline-width: 2px;
      outline-offset: 2px;
    }
    &:not(.collapse-close) {
      > input[type="checkbox"], > input[type="radio"]:not(:checked), > .collapse-title {
        cursor: pointer;
      }
    }
    &:focus:not(.collapse-close, .collapse[open]) > .collapse-title {
      cursor: unset;
    }
    &:is([open], :focus:not(.collapse-close)) > :where(.collapse-content), &:not(.collapse-close) > :where(input:is([type="checkbox"], [type="radio"]):checked ~ .collapse-content) {
      padding-bottom: 1rem;
      transition: padding 0.2s ease-out, background-color 0.2s ease-out;
    }
    &:is([open]) {
      &.collapse-arrow {
        > .collapse-title:after {
          transform: translateY(-50%) rotate(225deg);
        }
      }
    }
    &.collapse-open {
      &.collapse-arrow {
        > .collapse-title:after {
          transform: translateY(-50%) rotate(225deg);
        }
      }
      &.collapse-plus {
        > .collapse-title:after {
          content: "−";
        }
      }
    }
    &.collapse-arrow:focus:not(.collapse-close) {
      > .collapse-title:after {
        transform: translateY(-50%) rotate(225deg);
      }
    }
    &.collapse-arrow:not(.collapse-close) {
      > input:is([type="checkbox"], [type="radio"]):checked ~ .collapse-title:after {
        transform: translateY(-50%) rotate(225deg);
      }
    }
    &[open] {
      &.collapse-plus {
        > .collapse-title:after {
          content: "−";
        }
      }
    }
    &.collapse-plus:focus:not(.collapse-close) {
      > .collapse-title:after {
        content: "−";
      }
    }
    &.collapse-plus:not(.collapse-close) {
      > input:is([type="checkbox"], [type="radio"]):checked ~ .collapse-title:after {
        content: "−";
      }
    }
    &:is(details) {
      width: 100%;
      & summary {
        position: relative;
        display: block;
        &::-webkit-details-marker {
          display: none;
        }
      }
    }
    &:is(details) summary {
      outline: none;
    }
  }
  .collapse-content {
    grid-column-start: 1;
    grid-row-start: 1;
    visibility: hidden;
    grid-column-start: 1;
    grid-row-start: 2;
    min-height: 0;
    padding-left: 1rem;
    padding-right: 1rem;
    cursor: unset;
    transition: visibility 0.2s, padding 0.2s ease-out, background-color 0.2s ease-out;
  }
  .validator-hint {
    visibility: hidden;
    margin-top: calc(0.25rem * 2);
    font-size: 0.75rem;
  }
  .validator {
    &:user-valid, &:has(:user-valid) {
      &, &:focus, &:checked, &[aria-checked="true"], &:focus-within {
        --input-color: var(--color-success);
      }
    }
    &:user-invalid, &:has(:user-invalid), &[aria-invalid]:not([aria-invalid="false"]) {
      &, &:focus, &:checked, &[aria-checked="true"], &:focus-within {
        --input-color: var(--color-error);
      }
      & ~ .validator-hint {
        visibility: visible;
        display: block;
        color: var(--color-error);
      }
    }
  }
  .collapse-open {
    grid-template-rows: max-content 1fr;
    > .collapse-content {
      visibility: visible;
      min-height: fit-content;
      padding-bottom: 1rem;
      transition: padding 0.2s ease-out, background-color 0.2s ease-out;
    }
  }
  .collapse {
    visibility: collapse;
  }
  .visible {
    visibility: visible;
  }
  .list {
    display: flex;
    flex-direction: column;
    font-size: 0.875rem;
    :where(.list-row) {
      --list-grid-cols: minmax(0, auto) 1fr;
      position: relative;
      display: grid;
      grid-auto-flow: column;
      gap: calc(0.25rem * 4);
      border-radius: var(--radius-box);
      padding: calc(0.25rem * 4);
      word-break: break-word;
      grid-template-columns: var(--list-grid-cols);
      &:has(.list-col-grow:nth-child(1)) {
        --list-grid-cols: 1fr;
      }
      &:has(.list-col-grow:nth-child(2)) {
        --list-grid-cols: minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(3)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(4)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(5)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(6)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto)
        minmax(0, auto) 1fr;
      }
      :not(.list-col-wrap) {
        grid-row-start: 1;
      }
    }
    & > :not(:last-child) {
      &.list-row, .list-row {
        &:after {
          content: "";
          border-bottom: var(--border) solid;
          inset-inline: var(--radius-box);
          position: absolute;
          bottom: calc(0.25rem * 0);
          border-color: var(--color-base-content);
          @supports (color: color-mix(in lab, red, red)) {
            border-color: color-mix(in oklab, var(--color-base-content) 5%, transparent);
          }
        }
      }
    }
  }
  .toast {
    position: fixed;
    inset-inline-start: auto;
    inset-inline-end: calc(0.25rem * 4);
    top: auto;
    bottom: calc(0.25rem * 4);
    display: flex;
    flex-direction: column;
    gap: calc(0.25rem * 2);
    background-color: transparent;
    translate: var(--toast-x, 0) var(--toast-y, 0);
    width: max-content;
    max-width: calc(100vw - 2rem);
    & > * {
      animation: toast 0.25s ease-out;
    }
    &:where(.toast-start) {
      inset-inline-start: calc(0.25rem * 4);
      inset-inline-end: auto;
      --toast-x: 0;
    }
    &:where(.toast-center) {
      inset-inline-start: calc(1/2 * 100%);
      inset-inline-end: calc(1/2 * 100%);
      --toast-x: -50%;
    }
    &:where(.toast-end) {
      inset-inline-start: auto;
      inset-inline-end: calc(0.25rem * 4);
      --toast-x: 0;
    }
    &:where(.toast-bottom) {
      top: auto;
      bottom: calc(0.25rem * 4);
      --toast-y: 0;
    }
    &:where(.toast-middle) {
      top: calc(1/2 * 100%);
      bottom: auto;
      --toast-y: -50%;
    }
    &:where(.toast-top) {
      top: calc(0.25rem * 4);
      bottom: auto;
      --toast-y: 0;
    }
  }
  .toggle {
    border: var(--border) solid currentColor;
    color: var(--input-color);
    position: relative;
    display: inline-grid;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    place-content: center;
    vertical-align: middle;
    webkit-user-select: none;
    user-select: none;
    grid-template-columns: 0fr 1fr 1fr;
    --radius-selector-max: calc(
    var(--radius-selector) + var(--radius-selector) + var(--radius-selector)
  );
    border-radius: calc( var(--radius-selector) + min(var(--toggle-p), var(--radius-selector-max)) + min(var(--border), var(--radius-selector-max)) );
    padding: var(--toggle-p);
    box-shadow: 0 1px currentColor inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000) inset;
    }
    transition: color 0.3s, grid-template-columns 0.2s;
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 50%, #0000);
    }
    --toggle-p: calc(var(--size) * 0.125);
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: calc((var(--size) * 2) - (var(--border) + var(--toggle-p)) * 2);
    height: var(--size);
    > * {
      z-index: 1;
      grid-column: span 1 / span 1;
      grid-column-start: 2;
      grid-row-start: 1;
      height: 100%;
      cursor: pointer;
      appearance: none;
      background-color: transparent;
      padding: calc(0.25rem * 0.5);
      transition: opacity 0.2s, rotate 0.4s;
      border: none;
      &:focus {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
      &:nth-child(2) {
        color: var(--color-base-100);
        rotate: 0deg;
      }
      &:nth-child(3) {
        color: var(--color-base-100);
        opacity: 0%;
        rotate: -15deg;
      }
    }
    &:has(:checked) {
      > :nth-child(2) {
        opacity: 0%;
        rotate: 15deg;
      }
      > :nth-child(3) {
        opacity: 100%;
        rotate: 0deg;
      }
    }
    &:before {
      position: relative;
      inset-inline-start: calc(0.25rem * 0);
      grid-column-start: 2;
      grid-row-start: 1;
      aspect-ratio: 1 / 1;
      height: 100%;
      border-radius: var(--radius-selector);
      background-color: currentColor;
      translate: 0;
      --tw-content: "";
      content: var(--tw-content);
      transition: background-color 0.1s, translate 0.2s, inset-inline-start 0.2s;
      box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px currentColor;
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000);
      }
      background-size: auto, calc(var(--noise) * 100%);
      background-image: none, var(--fx-noise);
    }
    @media (forced-colors: active) {
      &:before {
        outline-style: var(--tw-outline-style);
        outline-width: 1px;
        outline-offset: calc(1px * -1);
      }
    }
    @media print {
      &:before {
        outline: 0.25rem solid;
        outline-offset: -1rem;
      }
    }
    &:focus-visible, &:has(:focus-visible) {
      outline: 2px solid currentColor;
      outline-offset: 2px;
    }
    &:checked, &[aria-checked="true"], &:has(> input:checked) {
      grid-template-columns: 1fr 1fr 0fr;
      background-color: var(--color-base-100);
      --input-color: var(--color-base-content);
      &:before {
        background-color: currentColor;
      }
      @starting-style {
        &:before {
          opacity: 0;
        }
      }
    }
    &:indeterminate {
      grid-template-columns: 0.5fr 1fr 0.5fr;
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 30%;
      &:before {
        background-color: transparent;
        border: var(--border) solid currentColor;
      }
    }
  }
  .input {
    cursor: text;
    border: var(--border) solid #0000;
    position: relative;
    display: inline-flex;
    flex-shrink: 1;
    appearance: none;
    align-items: center;
    gap: calc(0.25rem * 2);
    background-color: var(--color-base-100);
    padding-inline: calc(0.25rem * 3);
    vertical-align: middle;
    white-space: nowrap;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    font-size: 0.875rem;
    touch-action: manipulation;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    border-color: var(--input-color);
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    --size: calc(var(--size-field, 0.25rem) * 10);
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    &:where(input) {
      display: inline-flex;
    }
    :where(input) {
      display: inline-flex;
      height: 100%;
      width: 100%;
      appearance: none;
      background-color: transparent;
      border: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    :where(input[type="url"]), :where(input[type="email"]) {
      direction: ltr;
    }
    :where(input[type="date"]) {
      display: inline-block;
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
      z-index: 1;
    }
    &:has(> input[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
      box-shadow: none;
    }
    &:has(> input[disabled]) > input[disabled] {
      cursor: not-allowed;
    }
    &::-webkit-date-and-time-value {
      text-align: inherit;
    }
    &[type="number"] {
      &::-webkit-inner-spin-button {
        margin-block: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * -3);
      }
    }
    &::-webkit-calendar-picker-indicator {
      position: absolute;
      inset-inline-end: 0.75em;
    }
  }
  .indicator {
    position: relative;
    display: inline-flex;
    width: max-content;
    :where(.indicator-item) {
      z-index: 1;
      position: absolute;
      white-space: nowrap;
      top: var(--indicator-t, 0);
      bottom: var(--indicator-b, auto);
      left: var(--indicator-s, auto);
      right: var(--indicator-e, 0);
      translate: var(--indicator-x, 50%) var(--indicator-y, -50%);
    }
  }
  .table {
    font-size: 0.875rem;
    position: relative;
    width: 100%;
    border-radius: var(--radius-box);
    text-align: left;
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      text-align: right;
    }
    tr.row-hover {
      &, &:nth-child(even) {
        &:hover {
          @media (hover: hover) {
            background-color: var(--color-base-200);
          }
        }
      }
    }
    :where(th, td) {
      padding-inline: calc(0.25rem * 4);
      padding-block: calc(0.25rem * 3);
      vertical-align: middle;
    }
    :where(thead, tfoot) {
      white-space: nowrap;
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
      }
      font-size: 0.875rem;
      font-weight: 600;
    }
    :where(tfoot) {
      border-top: var(--border) solid var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        border-top: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
      }
    }
    :where(.table-pin-rows thead tr) {
      position: sticky;
      top: calc(0.25rem * 0);
      z-index: 1;
      background-color: var(--color-base-100);
    }
    :where(.table-pin-rows tfoot tr) {
      position: sticky;
      bottom: calc(0.25rem * 0);
      z-index: 1;
      background-color: var(--color-base-100);
    }
    :where(.table-pin-cols tr th) {
      position: sticky;
      right: calc(0.25rem * 0);
      left: calc(0.25rem * 0);
      background-color: var(--color-base-100);
    }
    :where(thead tr, tbody tr:not(:last-child)) {
      border-bottom: var(--border) solid var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        border-bottom: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
      }
    }
  }
  .steps {
    display: inline-grid;
    grid-auto-flow: column;
    overflow: hidden;
    overflow-x: auto;
    counter-reset: step;
    grid-auto-columns: 1fr;
    .step {
      display: grid;
      grid-template-columns: repeat(1, minmax(0, 1fr));
      grid-template-columns: auto;
      grid-template-rows: repeat(2, minmax(0, 1fr));
      grid-template-rows: 40px 1fr;
      place-items: center;
      text-align: center;
      min-width: 4rem;
      --step-bg: var(--color-base-300);
      --step-fg: var(--color-base-content);
      &:before {
        top: calc(0.25rem * 0);
        grid-column-start: 1;
        grid-row-start: 1;
        height: calc(0.25rem * 2);
        width: 100%;
        border: 1px solid;
        color: var(--step-bg);
        background-color: var(--step-bg);
        --tw-content: "";
        content: var(--tw-content);
        margin-inline-start: -100%;
      }
      > .step-icon, &:not(:has(.step-icon)):after {
        content: counter(step);
        counter-increment: step;
        z-index: 1;
        color: var(--step-fg);
        background-color: var(--step-bg);
        border: 1px solid var(--step-bg);
        position: relative;
        grid-column-start: 1;
        grid-row-start: 1;
        display: grid;
        height: calc(0.25rem * 8);
        width: calc(0.25rem * 8);
        place-items: center;
        place-self: center;
        border-radius: calc(infinity * 1px);
      }
      &:first-child:before {
        content: none;
      }
      &[data-content]:after {
        content: attr(data-content);
      }
    }
    .step-neutral {
      + .step-neutral:before, &:after, > .step-icon {
        --step-bg: var(--color-neutral);
        --step-fg: var(--color-neutral-content);
      }
    }
    .step-primary {
      + .step-primary:before, &:after, > .step-icon {
        --step-bg: var(--color-primary);
        --step-fg: var(--color-primary-content);
      }
    }
    .step-secondary {
      + .step-secondary:before, &:after, > .step-icon {
        --step-bg: var(--color-secondary);
        --step-fg: var(--color-secondary-content);
      }
    }
    .step-accent {
      + .step-accent:before, &:after, > .step-icon {
        --step-bg: var(--color-accent);
        --step-fg: var(--color-accent-content);
      }
    }
    .step-info {
      + .step-info:before, &:after, > .step-icon {
        --step-bg: var(--color-info);
        --step-fg: var(--color-info-content);
      }
    }
    .step-success {
      + .step-success:before, &:after, > .step-icon {
        --step-bg: var(--color-success);
        --step-fg: var(--color-success-content);
      }
    }
    .step-warning {
      + .step-warning:before, &:after, > .step-icon {
        --step-bg: var(--color-warning);
        --step-fg: var(--color-warning-content);
      }
    }
    .step-error {
      + .step-error:before, &:after, > .step-icon {
        --step-bg: var(--color-error);
        --step-fg: var(--color-error-content);
      }
    }
  }
  .range {
    appearance: none;
    webkit-appearance: none;
    --range-thumb: var(--color-base-100);
    --range-thumb-size: calc(var(--size-selector, 0.25rem) * 6);
    --range-progress: currentColor;
    --range-fill: 1;
    --range-p: 0.25rem;
    --range-bg: currentColor;
    @supports (color: color-mix(in lab, red, red)) {
      --range-bg: color-mix(in oklab, currentColor 10%, #0000);
    }
    cursor: pointer;
    overflow: hidden;
    background-color: transparent;
    vertical-align: middle;
    width: clamp(3rem, 20rem, 100%);
    --radius-selector-max: calc(
    var(--radius-selector) + var(--radius-selector) + var(--radius-selector)
  );
    border-radius: calc(var(--radius-selector) + min(var(--range-p), var(--radius-selector-max)));
    border: none;
    height: var(--range-thumb-size);
    [dir="rtl"] & {
      --range-dir: -1;
    }
    &:focus {
      outline: none;
    }
    &:focus-visible {
      outline: 2px solid;
      outline-offset: 2px;
    }
    &::-webkit-slider-runnable-track {
      width: 100%;
      background-color: var(--range-bg);
      border-radius: var(--radius-selector);
      height: calc(var(--range-thumb-size) * 0.5);
    }
    @media (forced-colors: active) {
      &::-webkit-slider-runnable-track {
        border: 1px solid;
      }
    }
    @media (forced-colors: active) {
      &::-moz-range-track {
        border: 1px solid;
      }
    }
    &::-webkit-slider-thumb {
      position: relative;
      box-sizing: border-box;
      border-radius: calc(var(--radius-selector) + min(var(--range-p), var(--radius-selector-max)));
      background-color: currentColor;
      height: var(--range-thumb-size);
      width: var(--range-thumb-size);
      border: var(--range-p) solid;
      appearance: none;
      webkit-appearance: none;
      top: 50%;
      color: var(--range-progress);
      transform: translateY(-50%);
      box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px currentColor, 0 0 0 2rem var(--range-thumb) inset, calc((var(--range-dir, 1) * -100rem) - (var(--range-dir, 1) * var(--range-thumb-size) / 2)) 0 0 calc(100rem * var(--range-fill));
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000), 0 0 0 2rem var(--range-thumb) inset, calc((var(--range-dir, 1) * -100rem) - (var(--range-dir, 1) * var(--range-thumb-size) / 2)) 0 0 calc(100rem * var(--range-fill));
      }
    }
    &::-moz-range-track {
      width: 100%;
      background-color: var(--range-bg);
      border-radius: var(--radius-selector);
      height: calc(var(--range-thumb-size) * 0.5);
    }
    &::-moz-range-thumb {
      position: relative;
      box-sizing: border-box;
      border-radius: calc(var(--radius-selector) + min(var(--range-p), var(--radius-selector-max)));
      background-color: currentColor;
      height: var(--range-thumb-size);
      width: var(--range-thumb-size);
      border: var(--range-p) solid;
      top: 50%;
      color: var(--range-progress);
      box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px currentColor, 0 0 0 2rem var(--range-thumb) inset, calc((var(--range-dir, 1) * -100rem) - (var(--range-dir, 1) * var(--range-thumb-size) / 2)) 0 0 calc(100rem * var(--range-fill));
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000), 0 0 0 2rem var(--range-thumb) inset, calc((var(--range-dir, 1) * -100rem) - (var(--range-dir, 1) * var(--range-thumb-size) / 2)) 0 0 calc(100rem * var(--range-fill));
      }
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 30%;
    }
  }
  .countdown {
    display: inline-flex;
    &.countdown {
      line-height: 1em;
    }
    & > * {
      display: inline-block;
      overflow-y: hidden;
      height: 1em;
      &:before {
        position: relative;
        content: "00\A 01\A 02\A 03\A 04\A 05\A 06\A 07\A 08\A 09\A 10\A 11\A 12\A 13\A 14\A 15\A 16\A 17\A 18\A 19\A 20\A 21\A 22\A 23\A 24\A 25\A 26\A 27\A 28\A 29\A 30\A 31\A 32\A 33\A 34\A 35\A 36\A 37\A 38\A 39\A 40\A 41\A 42\A 43\A 44\A 45\A 46\A 47\A 48\A 49\A 50\A 51\A 52\A 53\A 54\A 55\A 56\A 57\A 58\A 59\A 60\A 61\A 62\A 63\A 64\A 65\A 66\A 67\A 68\A 69\A 70\A 71\A 72\A 73\A 74\A 75\A 76\A 77\A 78\A 79\A 80\A 81\A 82\A 83\A 84\A 85\A 86\A 87\A 88\A 89\A 90\A 91\A 92\A 93\A 94\A 95\A 96\A 97\A 98\A 99\A";
        white-space: pre;
        top: calc(var(--value) * -1em);
        text-align: center;
        transition: all 1s cubic-bezier(1, 0, 0, 1);
      }
    }
  }
  .select {
    border: var(--border) solid #0000;
    position: relative;
    display: inline-flex;
    flex-shrink: 1;
    appearance: none;
    align-items: center;
    gap: calc(0.25rem * 1.5);
    background-color: var(--color-base-100);
    padding-inline-start: calc(0.25rem * 4);
    padding-inline-end: calc(0.25rem * 7);
    vertical-align: middle;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    font-size: 0.875rem;
    touch-action: manipulation;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    background-image: linear-gradient(45deg, #0000 50%, currentColor 50%), linear-gradient(135deg, currentColor 50%, #0000 50%);
    background-position: calc(100% - 20px) calc(1px + 50%), calc(100% - 16.1px) calc(1px + 50%);
    background-size: 4px 4px, 4px 4px;
    background-repeat: no-repeat;
    text-overflow: ellipsis;
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    border-color: var(--input-color);
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    --size: calc(var(--size-field, 0.25rem) * 10);
    [dir="rtl"] & {
      background-position: calc(0% + 12px) calc(1px + 50%), calc(0% + 16px) calc(1px + 50%);
    }
    select {
      margin-inline-start: calc(0.25rem * -4);
      margin-inline-end: calc(0.25rem * -7);
      width: calc(100% + 2.75rem);
      appearance: none;
      padding-inline-start: calc(0.25rem * 4);
      padding-inline-end: calc(0.25rem * 7);
      height: calc(100% - 2px);
      background: inherit;
      border-radius: inherit;
      border-style: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
      &:not(:last-child) {
        margin-inline-end: calc(0.25rem * -5.5);
        background-image: none;
      }
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
      z-index: 1;
    }
    &:has(> select[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
    }
    &:has(> select[disabled]) > select[disabled] {
      cursor: not-allowed;
    }
  }
  .card {
    position: relative;
    display: flex;
    flex-direction: column;
    border-radius: var(--radius-box);
    outline-width: 2px;
    transition: outline 0.2s ease-in-out;
    outline: 0 solid #0000;
    outline-offset: 2px;
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    &:focus-visible {
      outline-color: currentColor;
    }
    :where(figure:first-child) {
      overflow: hidden;
      border-start-start-radius: inherit;
      border-start-end-radius: inherit;
      border-end-start-radius: unset;
      border-end-end-radius: unset;
    }
    :where(figure:last-child) {
      overflow: hidden;
      border-start-start-radius: unset;
      border-start-end-radius: unset;
      border-end-start-radius: inherit;
      border-end-end-radius: inherit;
    }
    &:where(.card-border) {
      border: var(--border) solid var(--color-base-200);
    }
    &:where(.card-dash) {
      border: var(--border) dashed var(--color-base-200);
    }
    &.image-full {
      display: grid;
      > * {
        grid-column-start: 1;
        grid-row-start: 1;
      }
      > .card-body {
        position: relative;
        color: var(--color-neutral-content);
      }
      :where(figure) {
        overflow: hidden;
        border-radius: inherit;
      }
      > figure img {
        height: 100%;
        object-fit: cover;
        filter: brightness(28%);
      }
    }
    figure {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    &:has(> input:is(input[type="checkbox"], input[type="radio"])) {
      cursor: pointer;
      user-select: none;
    }
    &:has(> :checked) {
      outline: 2px solid currentColor;
    }
  }
  .swap {
    position: relative;
    display: inline-grid;
    cursor: pointer;
    place-content: center;
    vertical-align: middle;
    webkit-user-select: none;
    user-select: none;
    input {
      appearance: none;
      border: none;
    }
    > * {
      grid-column-start: 1;
      grid-row-start: 1;
      transition-property: transform, rotate, opacity;
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
    .swap-on, .swap-indeterminate, input:indeterminate ~ .swap-on {
      opacity: 0%;
    }
    input:is(:checked, :indeterminate) {
      & ~ .swap-off {
        opacity: 0%;
      }
    }
    input:checked ~ .swap-on, input:indeterminate ~ .swap-indeterminate {
      opacity: 100%;
      backface-visibility: visible;
    }
  }
  .collapse-title {
    grid-column-start: 1;
    grid-row-start: 1;
    position: relative;
    width: 100%;
    padding: 1rem;
    padding-inline-end: 3rem;
    min-height: 1lh;
    transition: background-color 0.2s ease-out;
  }
  .menu-horizontal {
    display: inline-flex;
    flex-direction: row;
    & > li:not(.menu-title) > details > ul {
      position: absolute;
      margin-inline-start: calc(0.25rem * 0);
      margin-top: calc(0.25rem * 4);
      padding-block: calc(0.25rem * 2);
      padding-inline-end: calc(0.25rem * 2);
    }
    & > li > details > ul {
      &:before {
        content: none;
      }
    }
    :where(& > li:not(.menu-title) > details > ul) {
      border-radius: var(--radius-box);
      background-color: var(--color-base-100);
      box-shadow: 0 1px 3px 0 oklch(0% 0 0/0.1), 0 1px 2px -1px oklch(0% 0 0/0.1);
    }
  }
  .avatar {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
    & > div {
      display: block;
      aspect-ratio: 1 / 1;
      overflow: hidden;
    }
    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
  .checkbox {
    border: var(--border) solid var(--input-color, var(--color-base-content));
    @supports (color: color-mix(in lab, red, red)) {
      border: var(--border) solid var(--input-color, color-mix(in oklab, var(--color-base-content) 20%, #0000));
    }
    position: relative;
    display: inline-block;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    border-radius: var(--radius-selector);
    padding: calc(0.25rem * 1);
    vertical-align: middle;
    color: var(--color-base-content);
    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 0 #0000 inset, 0 0 #0000;
    transition: background-color 0.2s, box-shadow 0.2s;
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: var(--size);
    height: var(--size);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    &:before {
      --tw-content: "";
      content: var(--tw-content);
      display: block;
      width: 100%;
      height: 100%;
      rotate: 45deg;
      background-color: currentColor;
      opacity: 0%;
      transition: clip-path 0.3s, opacity 0.1s, rotate 0.3s, translate 0.3s;
      transition-delay: 0.1s;
      clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 70% 80%, 70% 100%);
      box-shadow: 0px 3px 0 0px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
      font-size: 1rem;
      line-height: 0.75;
    }
    &:focus-visible {
      outline: 2px solid var(--input-color, currentColor);
      outline-offset: 2px;
    }
    &:checked, &[aria-checked="true"] {
      background-color: var(--input-color, #0000);
      box-shadow: 0 0 #0000 inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1));
      &:before {
        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 0%, 70% 0%, 70% 100%);
        opacity: 100%;
      }
      @media (forced-colors: active) {
        &:before {
          rotate: 0deg;
          background-color: transparent;
          --tw-content: "✔︎";
          clip-path: none;
        }
      }
      @media print {
        &:before {
          rotate: 0deg;
          background-color: transparent;
          --tw-content: "✔︎";
          clip-path: none;
        }
      }
    }
    &:indeterminate {
      &:before {
        rotate: 0deg;
        opacity: 100%;
        translate: 0 -35%;
        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 80% 80%, 80% 100%);
      }
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 20%;
    }
  }
  .radio {
    position: relative;
    display: inline-block;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    border-radius: calc(infinity * 1px);
    padding: calc(0.25rem * 1);
    vertical-align: middle;
    border: var(--border) solid var(--input-color, currentColor);
    @supports (color: color-mix(in lab, red, red)) {
      border: var(--border) solid var(--input-color, color-mix(in srgb, currentColor 20%, #0000));
    }
    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset;
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: var(--size);
    height: var(--size);
    color: var(--input-color, currentColor);
    &:before {
      display: block;
      width: 100%;
      height: 100%;
      border-radius: calc(infinity * 1px);
      --tw-content: "";
      content: var(--tw-content);
      background-size: auto, calc(var(--noise) * 100%);
      background-image: none, var(--fx-noise);
    }
    &:focus-visible {
      outline: 2px solid currentColor;
    }
    &:checked, &[aria-checked="true"] {
      animation: radio 0.2s ease-out;
      border-color: currentColor;
      background-color: var(--color-base-100);
      &:before {
        background-color: currentColor;
        box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1));
      }
      @media (forced-colors: active) {
        &:before {
          outline-style: var(--tw-outline-style);
          outline-width: 1px;
          outline-offset: calc(1px * -1);
        }
      }
      @media print {
        &:before {
          outline: 0.25rem solid;
          outline-offset: -1rem;
        }
      }
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 20%;
    }
  }
  .rating {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
    & input {
      border: none;
      appearance: none;
    }
    :where(*) {
      animation: rating 0.25s ease-out;
      height: calc(0.25rem * 6);
      width: calc(0.25rem * 6);
      border-radius: 0;
      background-color: var(--color-base-content);
      opacity: 20%;
      &:is(input) {
        cursor: pointer;
      }
    }
    & .rating-hidden {
      width: calc(0.25rem * 2);
      background-color: transparent;
    }
    input[type="radio"]:checked {
      background-image: none;
    }
    * {
      &:checked, &[aria-checked="true"], &[aria-current="true"], &:has(~ *:checked, ~ *[aria-checked="true"], ~ *[aria-current="true"]) {
        opacity: 100%;
      }
      &:focus-visible {
        transition: scale 0.2s ease-out;
        scale: 1.1;
      }
    }
    & *:active:focus {
      animation: none;
      scale: 1.1;
    }
    &.rating-xs :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 4);
      height: calc(0.25rem * 4);
    }
    &.rating-sm :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 5);
      height: calc(0.25rem * 5);
    }
    &.rating-md :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 6);
      height: calc(0.25rem * 6);
    }
    &.rating-lg :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 7);
      height: calc(0.25rem * 7);
    }
    &.rating-xl :where(*:not(.rating-hidden)) {
      width: calc(0.25rem * 8);
      height: calc(0.25rem * 8);
    }
  }
  .stats {
    position: relative;
    display: inline-grid;
    grid-auto-flow: column;
    overflow-x: auto;
    border-radius: var(--radius-box);
  }
  .progress {
    position: relative;
    height: calc(0.25rem * 2);
    width: 100%;
    appearance: none;
    overflow: hidden;
    border-radius: var(--radius-box);
    background-color: currentColor;
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, currentColor 20%, transparent);
    }
    color: var(--color-base-content);
    &:indeterminate {
      background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );
      background-size: 200%;
      background-position-x: 15%;
      animation: progress 5s ease-in-out infinite;
      @supports (-moz-appearance: none) {
        &::-moz-progress-bar {
          background-color: transparent;
          background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );
          background-size: 200%;
          background-position-x: 15%;
          animation: progress 5s ease-in-out infinite;
        }
      }
    }
    @supports (-moz-appearance: none) {
      &::-moz-progress-bar {
        border-radius: var(--radius-box);
        background-color: currentColor;
      }
    }
    @supports (-webkit-appearance: none) {
      &::-webkit-progress-bar {
        border-radius: var(--radius-box);
        background-color: transparent;
      }
      &::-webkit-progress-value {
        border-radius: var(--radius-box);
        background-color: currentColor;
      }
    }
  }
  .progress\! {
    position: relative !important;
    height: calc(0.25rem * 2) !important;
    width: 100% !important;
    appearance: none !important;
    overflow: hidden !important;
    border-radius: var(--radius-box) !important;
    background-color: currentColor !important;
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, currentColor 20%, transparent) !important;
    }
    color: var(--color-base-content) !important;
    &:indeterminate {
      background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% ) !important;
      background-size: 200% !important;
      background-position-x: 15% !important;
      animation: progress 5s ease-in-out infinite !important;
      @supports (-moz-appearance: none) {
        &::-moz-progress-bar {
          background-color: transparent !important;
          background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% ) !important;
          background-size: 200% !important;
          background-position-x: 15% !important;
          animation: progress 5s ease-in-out infinite !important;
        }
      }
    }
    @supports (-moz-appearance: none) {
      &::-moz-progress-bar {
        border-radius: var(--radius-box) !important;
        background-color: currentColor !important;
      }
    }
    @supports (-webkit-appearance: none) {
      &::-webkit-progress-bar {
        border-radius: var(--radius-box) !important;
        background-color: transparent !important;
      }
      &::-webkit-progress-value {
        border-radius: var(--radius-box) !important;
        background-color: currentColor !important;
      }
    }
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .dropdown-right {
    --anchor-h: right;
    --anchor-v: span-bottom;
    .dropdown-content {
      inset-inline-start: 100%;
      top: calc(0.25rem * 0);
      bottom: auto;
      transform-origin: left;
    }
  }
  .dropdown-left {
    --anchor-h: left;
    --anchor-v: span-bottom;
    .dropdown-content {
      inset-inline-end: 100%;
      top: calc(0.25rem * 0);
      bottom: auto;
      transform-origin: right;
    }
  }
  .dropdown-end {
    --anchor-h: span-left;
    :where(.dropdown-content) {
      inset-inline-end: calc(0.25rem * 0);
      translate: 0 0;
      [dir="rtl"] & {
        translate: 0 0;
      }
    }
    &.dropdown-left {
      --anchor-h: left;
      --anchor-v: span-top;
      .dropdown-content {
        top: auto;
        bottom: calc(0.25rem * 0);
      }
    }
    &.dropdown-right {
      --anchor-h: right;
      --anchor-v: span-top;
      .dropdown-content {
        top: auto;
        bottom: calc(0.25rem * 0);
      }
    }
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-1 {
    right: calc(var(--spacing) * 1);
  }
  .bottom-1 {
    bottom: calc(var(--spacing) * 1);
  }
  .file-input {
    cursor: pointer;
    cursor: pointer;
    border: var(--border) solid #0000;
    display: inline-flex;
    appearance: none;
    align-items: center;
    background-color: var(--color-base-100);
    vertical-align: middle;
    webkit-user-select: none;
    user-select: none;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    padding-inline-end: 0.75rem;
    font-size: 0.875rem;
    line-height: 2;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    border-color: var(--input-color);
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    --size: calc(var(--size-field, 0.25rem) * 10);
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    &::file-selector-button {
      margin-inline-end: calc(0.25rem * 4);
      cursor: pointer;
      padding-inline: calc(0.25rem * 4);
      webkit-user-select: none;
      user-select: none;
      height: calc(100% + var(--border) * 2);
      margin-block: calc(var(--border) * -1);
      margin-inline-start: calc(var(--border) * -1);
      font-size: 0.875rem;
      color: var(--btn-fg);
      border-width: var(--border);
      border-style: solid;
      border-color: var(--btn-border);
      border-start-start-radius: calc(var(--join-ss, var(--radius-field) - var(--border)));
      border-end-start-radius: calc(var(--join-es, var(--radius-field) - var(--border)));
      font-weight: 600;
      background-color: var(--btn-bg);
      background-size: calc(var(--noise) * 100%);
      background-image: var(--btn-noise);
      text-shadow: 0 0.5px oklch(1 0 0 / calc(var(--depth) * 0.15));
      box-shadow: 0 0.5px 0 0.5px white inset, var(--btn-shadow);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 0.5px 0 0.5px color-mix( in oklab, color-mix(in oklab, white 30%, var(--btn-bg)) calc(var(--depth) * 20%), #0000 ) inset, var(--btn-shadow);
      }
      --size: calc(var(--size-field, 0.25rem) * 10);
      --btn-bg: var(--btn-color, var(--color-base-200));
      --btn-fg: var(--color-base-content);
      --btn-border: var(--btn-bg);
      @supports (color: color-mix(in lab, red, red)) {
        --btn-border: color-mix(in oklab, var(--btn-bg), #000 5%);
      }
      --btn-shadow: 0 3px 2px -2px var(--btn-bg),
      0 4px 3px -2px var(--btn-bg);
      @supports (color: color-mix(in lab, red, red)) {
        --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) 30%, #0000),
      0 4px 3px -2px color-mix(in oklab, var(--btn-bg) 30%, #0000);
      }
      --btn-noise: var(--fx-noise);
    }
    &:focus {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) 10%, #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
    }
    &:has(> input[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
      box-shadow: none;
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklch, var(--color-base-content) 20%, #0000);
      }
      &::file-selector-button {
        cursor: not-allowed;
        border-color: var(--color-base-200);
        background-color: var(--color-base-200);
        --btn-border: #0000;
        --btn-noise: none;
        --btn-fg: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
        }
      }
    }
  }
  .textarea {
    border: var(--border) solid #0000;
    min-height: calc(0.25rem * 20);
    flex-shrink: 1;
    appearance: none;
    border-radius: var(--radius-field);
    background-color: var(--color-base-100);
    padding-block: calc(0.25rem * 2);
    vertical-align: middle;
    width: clamp(3rem, 20rem, 100%);
    padding-inline-start: 0.75rem;
    padding-inline-end: 0.75rem;
    font-size: 0.875rem;
    touch-action: manipulation;
    border-color: var(--input-color);
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    textarea {
      appearance: none;
      background-color: transparent;
      border: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
    }
    &:has(> textarea[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
      box-shadow: none;
    }
    &:has(> textarea[disabled]) > textarea[disabled] {
      cursor: not-allowed;
    }
  }
  .btn-active {
    --btn-bg: var(--btn-color, var(--color-base-200));
    @supports (color: color-mix(in lab, red, red)) {
      --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
    }
    --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);
    isolation: isolate;
  }
  .modal-backdrop {
    grid-column-start: 1;
    grid-row-start: 1;
    display: grid;
    align-self: stretch;
    justify-self: stretch;
    color: transparent;
    z-index: -1;
    button {
      cursor: pointer;
    }
  }
  .z-50 {
    z-index: 50;
  }
  .z-\[1\] {
    z-index: 1;
  }
  .stat-figure {
    grid-column-start: 2;
    grid-row: span 3 / span 3;
    grid-row-start: 1;
    place-self: center;
    justify-self: flex-end;
  }
  .modal-box {
    grid-column-start: 1;
    grid-row-start: 1;
    max-height: 100vh;
    width: calc(11/12 * 100%);
    max-width: 32rem;
    background-color: var(--color-base-100);
    padding: calc(0.25rem * 6);
    transition: translate 0.3s ease-out, scale 0.3s ease-out, opacity 0.2s ease-out 0.05s, box-shadow 0.3s ease-out;
    border-top-left-radius: var(--modal-tl, var(--radius-box));
    border-top-right-radius: var(--modal-tr, var(--radius-box));
    border-bottom-left-radius: var(--modal-bl, var(--radius-box));
    border-bottom-right-radius: var(--modal-br, var(--radius-box));
    scale: 95%;
    opacity: 0;
    box-shadow: oklch(0% 0 0/ 0.25) 0px 25px 50px -12px;
    overflow-y: auto;
    overscroll-behavior: contain;
  }
  .drawer-content {
    grid-column-start: 2;
    grid-row-start: 1;
    min-width: calc(0.25rem * 0);
  }
  .stat-value {
    grid-column-start: 1;
    white-space: nowrap;
    font-size: 2rem;
    font-weight: 800;
  }
  .stat-desc {
    grid-column-start: 1;
    white-space: nowrap;
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
    }
    font-size: 0.75rem;
  }
  .stat-title {
    grid-column-start: 1;
    white-space: nowrap;
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
    }
    font-size: 0.75rem;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .divider {
    display: flex;
    height: calc(0.25rem * 4);
    flex-direction: row;
    align-items: center;
    align-self: stretch;
    white-space: nowrap;
    margin: var(--divider-m, 1rem 0);
    --divider-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --divider-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
    &:before, &:after {
      content: "";
      height: calc(0.25rem * 0.5);
      width: 100%;
      flex-grow: 1;
      background-color: var(--divider-color);
    }
    @media print {
      &:before, &:after {
        border: 0.5px solid;
      }
    }
    &:not(:empty) {
      gap: calc(0.25rem * 4);
    }
  }
  .m-4 {
    margin: calc(var(--spacing) * 4);
  }
  .filter {
    display: flex;
    flex-wrap: wrap;
    input[type="radio"] {
      width: auto;
    }
    input {
      overflow: hidden;
      opacity: 100%;
      scale: 1;
      transition: margin 0.1s, opacity 0.3s, padding 0.3s, border-width 0.1s;
      &:not(:last-child) {
        margin-inline-end: calc(0.25rem * 1);
      }
      &.filter-reset {
        aspect-ratio: 1 / 1;
        &::after {
          content: "×";
        }
      }
    }
    &:not(:has(input:checked:not(.filter-reset))) {
      .filter-reset, input[type="reset"] {
        scale: 0;
        border-width: 0;
        margin-inline: calc(0.25rem * 0);
        width: calc(0.25rem * 0);
        padding-inline: calc(0.25rem * 0);
        opacity: 0%;
      }
    }
    &:has(input:checked:not(.filter-reset)) {
      input:not(:checked, .filter-reset, input[type="reset"]) {
        scale: 0;
        border-width: 0;
        margin-inline: calc(0.25rem * 0);
        width: calc(0.25rem * 0);
        padding-inline: calc(0.25rem * 0);
        opacity: 0%;
      }
    }
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .label {
    display: inline-flex;
    align-items: center;
    gap: calc(0.25rem * 1.5);
    white-space: nowrap;
    color: currentColor;
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, currentColor 60%, transparent);
    }
    &:has(input) {
      cursor: pointer;
    }
    &:is(.input > *, .select > *) {
      display: flex;
      height: calc(100% - 0.5rem);
      align-items: center;
      padding-inline: calc(0.25rem * 3);
      white-space: nowrap;
      font-size: inherit;
      &:first-child {
        margin-inline-start: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * 3);
        border-inline-end: var(--border) solid currentColor;
        @supports (color: color-mix(in lab, red, red)) {
          border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
        }
      }
      &:last-child {
        margin-inline-start: calc(0.25rem * 3);
        margin-inline-end: calc(0.25rem * -3);
        border-inline-start: var(--border) solid currentColor;
        @supports (color: color-mix(in lab, red, red)) {
          border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
        }
      }
    }
  }
  .join-item {
    &:where(*:not(:first-child, :disabled, [disabled], .btn-disabled)) {
      margin-inline-start: calc(var(--border, 1px) * -1);
      margin-block-start: 0;
    }
    &:where(*:is(:disabled, [disabled], .btn-disabled)) {
      border-width: var(--border, 1px) 0 var(--border, 1px) var(--border, 1px);
    }
  }
  .prose {
    color: var(--tw-prose-body);
    max-width: 65ch;
    :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-lead);
      font-size: 1.25em;
      line-height: 1.6;
      margin-top: 1.2em;
      margin-bottom: 1.2em;
    }
    :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-links);
      text-decoration: underline;
      font-weight: 500;
    }
    :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-bold);
      font-weight: 600;
    }
    :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: decimal;
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-inline-start: 1.625em;
    }
    :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-alpha;
    }
    :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-alpha;
    }
    :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-alpha;
    }
    :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-alpha;
    }
    :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-roman;
    }
    :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-roman;
    }
    :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: upper-roman;
    }
    :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: lower-roman;
    }
    :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: decimal;
    }
    :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      list-style-type: disc;
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-inline-start: 1.625em;
    }
    :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
      font-weight: 400;
      color: var(--tw-prose-counters);
    }
    :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {
      color: var(--tw-prose-bullets);
    }
    :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      margin-top: 1.25em;
    }
    :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-color: var(--tw-prose-hr);
      border-top-width: 1;
      margin-top: 3em;
      margin-bottom: 3em;
    }
    :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 500;
      font-style: italic;
      color: var(--tw-prose-quotes);
      border-inline-start-width: 0.25rem;
      border-inline-start-color: var(--tw-prose-quote-borders);
      quotes: "\201C""\201D""\2018""\2019";
      margin-top: 1.6em;
      margin-bottom: 1.6em;
      padding-inline-start: 1em;
    }
    :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: open-quote;
    }
    :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: close-quote;
    }
    :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 800;
      font-size: 2.25em;
      margin-top: 0;
      margin-bottom: 0.8888889em;
      line-height: 1.1111111;
    }
    :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 900;
      color: inherit;
    }
    :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 700;
      font-size: 1.5em;
      margin-top: 2em;
      margin-bottom: 1em;
      line-height: 1.3333333;
    }
    :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 800;
      color: inherit;
    }
    :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      font-size: 1.25em;
      margin-top: 1.6em;
      margin-bottom: 0.6em;
      line-height: 1.6;
    }
    :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 700;
      color: inherit;
    }
    :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      margin-top: 1.5em;
      margin-bottom: 0.5em;
      line-height: 1.5;
    }
    :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 700;
      color: inherit;
    }
    :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      display: block;
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      font-weight: 500;
      font-family: inherit;
      color: var(--tw-prose-kbd);
      box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
      font-size: 0.875em;
      border-radius: 0.3125rem;
      padding-top: 0.1875em;
      padding-inline-end: 0.375em;
      padding-bottom: 0.1875em;
      padding-inline-start: 0.375em;
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-code);
      font-weight: 600;
      font-size: 0.875em;
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: "`";
    }
    :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: "`";
    }
    :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
      font-size: 0.875em;
    }
    :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
      font-size: 0.9em;
    }
    :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: inherit;
    }
    :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-pre-code);
      background-color: var(--tw-prose-pre-bg);
      overflow-x: auto;
      font-weight: 400;
      font-size: 0.875em;
      line-height: 1.7142857;
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
      border-radius: 0.375rem;
      padding-top: 0.8571429em;
      padding-inline-end: 1.1428571em;
      padding-bottom: 0.8571429em;
      padding-inline-start: 1.1428571em;
    }
    :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      background-color: transparent;
      border-width: 0;
      border-radius: 0;
      padding: 0;
      font-weight: inherit;
      color: inherit;
      font-size: inherit;
      font-family: inherit;
      line-height: inherit;
    }
    :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {
      content: none;
    }
    :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {
      content: none;
    }
    :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      width: 100%;
      table-layout: auto;
      margin-top: 2em;
      margin-bottom: 2em;
      font-size: 0.875em;
      line-height: 1.7142857;
    }
    :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 1px;
      border-bottom-color: var(--tw-prose-th-borders);
    }
    :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-headings);
      font-weight: 600;
      vertical-align: bottom;
      padding-inline-end: 0.5714286em;
      padding-bottom: 0.5714286em;
      padding-inline-start: 0.5714286em;
    }
    :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 1px;
      border-bottom-color: var(--tw-prose-td-borders);
    }
    :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-bottom-width: 0;
    }
    :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      vertical-align: baseline;
    }
    :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      border-top-width: 1px;
      border-top-color: var(--tw-prose-th-borders);
    }
    :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      vertical-align: top;
    }
    :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      text-align: start;
    }
    :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      color: var(--tw-prose-captions);
      font-size: 0.875em;
      line-height: 1.4285714;
      margin-top: 0.8571429em;
    }
    --tw-prose-body: oklch(37.3% 0.034 259.733);
    --tw-prose-headings: oklch(21% 0.034 264.665);
    --tw-prose-lead: oklch(44.6% 0.03 256.802);
    --tw-prose-links: oklch(21% 0.034 264.665);
    --tw-prose-bold: oklch(21% 0.034 264.665);
    --tw-prose-counters: oklch(55.1% 0.027 264.364);
    --tw-prose-bullets: oklch(87.2% 0.01 258.338);
    --tw-prose-hr: oklch(92.8% 0.006 264.531);
    --tw-prose-quotes: oklch(21% 0.034 264.665);
    --tw-prose-quote-borders: oklch(92.8% 0.006 264.531);
    --tw-prose-captions: oklch(55.1% 0.027 264.364);
    --tw-prose-kbd: oklch(21% 0.034 264.665);
    --tw-prose-kbd-shadows: NaN NaN NaN;
    --tw-prose-code: oklch(21% 0.034 264.665);
    --tw-prose-pre-code: oklch(92.8% 0.006 264.531);
    --tw-prose-pre-bg: oklch(27.8% 0.033 256.848);
    --tw-prose-th-borders: oklch(87.2% 0.01 258.338);
    --tw-prose-td-borders: oklch(92.8% 0.006 264.531);
    --tw-prose-invert-body: oklch(87.2% 0.01 258.338);
    --tw-prose-invert-headings: #fff;
    --tw-prose-invert-lead: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-links: #fff;
    --tw-prose-invert-bold: #fff;
    --tw-prose-invert-counters: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-bullets: oklch(44.6% 0.03 256.802);
    --tw-prose-invert-hr: oklch(37.3% 0.034 259.733);
    --tw-prose-invert-quotes: oklch(96.7% 0.003 264.542);
    --tw-prose-invert-quote-borders: oklch(37.3% 0.034 259.733);
    --tw-prose-invert-captions: oklch(70.7% 0.022 261.325);
    --tw-prose-invert-kbd: #fff;
    --tw-prose-invert-kbd-shadows: 255 255 255;
    --tw-prose-invert-code: #fff;
    --tw-prose-invert-pre-code: oklch(87.2% 0.01 258.338);
    --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
    --tw-prose-invert-th-borders: oklch(44.6% 0.03 256.802);
    --tw-prose-invert-td-borders: oklch(37.3% 0.034 259.733);
    font-size: 1rem;
    line-height: 1.75;
    :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }
    :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.375em;
    }
    :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0.375em;
    }
    :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
    }
    :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.25em;
    }
    :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
    }
    :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 1.25em;
    }
    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0.5em;
      padding-inline-start: 1.625em;
    }
    :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 0;
    }
    :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-top: 0.5714286em;
      padding-inline-end: 0.5714286em;
      padding-bottom: 0.5714286em;
      padding-inline-start: 0.5714286em;
    }
    :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-start: 0;
    }
    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      padding-inline-end: 0;
    }
    :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-top: 0;
    }
    :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {
      margin-bottom: 0;
    }
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .ml-auto {
    margin-left: auto;
  }
  .status {
    display: inline-block;
    aspect-ratio: 1 / 1;
    width: calc(0.25rem * 2);
    height: calc(0.25rem * 2);
    border-radius: var(--radius-selector);
    background-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
    background-position: center;
    background-repeat: no-repeat;
    vertical-align: middle;
    color: color-mix(in srgb, #000 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in srgb, #000 30%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-black) 30%, transparent);
      }
    }
    background-image: radial-gradient( circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * 0.5)), #0000 );
    box-shadow: 0 2px 3px -1px currentColor;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), #0000);
    }
  }
  .badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 2);
    border-radius: var(--radius-selector);
    vertical-align: middle;
    color: var(--badge-fg);
    border: var(--border) solid var(--badge-color, var(--color-base-200));
    font-size: 0.875rem;
    width: fit-content;
    padding-inline: calc(0.25rem * 3 - var(--border));
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    background-color: var(--badge-bg);
    --badge-bg: var(--badge-color, var(--color-base-100));
    --badge-fg: var(--color-base-content);
    --size: calc(var(--size-selector, 0.25rem) * 6);
    height: var(--size);
  }
  .navbar {
    display: flex;
    width: 100%;
    align-items: center;
    padding: 0.5rem;
    min-height: 4rem;
  }
  .footer {
    display: grid;
    width: 100%;
    grid-auto-flow: row;
    place-items: start;
    column-gap: calc(0.25rem * 4);
    row-gap: calc(0.25rem * 10);
    font-size: 0.875rem;
    line-height: 1.25rem;
    & > * {
      display: grid;
      place-items: start;
      gap: calc(0.25rem * 2);
    }
    &.footer-center {
      grid-auto-flow: column dense;
      place-items: center;
      text-align: center;
      & > * {
        place-items: center;
      }
    }
  }
  .stat {
    display: inline-grid;
    width: 100%;
    column-gap: calc(0.25rem * 4);
    padding-inline: calc(0.25rem * 6);
    padding-block: calc(0.25rem * 4);
    grid-template-columns: repeat(1, 1fr);
    &:not(:last-child) {
      border-inline-end: var(--border) dashed currentColor;
      @supports (color: color-mix(in lab, red, red)) {
        border-inline-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);
      }
      border-block-end: none;
    }
  }
  .card-body {
    display: flex;
    flex: auto;
    flex-direction: column;
    gap: calc(0.25rem * 2);
    padding: var(--card-p, 1.5rem);
    font-size: var(--card-fs, 0.875rem);
    :where(p) {
      flex-grow: 1;
    }
  }
  .alert {
    display: grid;
    align-items: center;
    gap: calc(0.25rem * 4);
    border-radius: var(--radius-box);
    padding-inline: calc(0.25rem * 4);
    padding-block: calc(0.25rem * 3);
    color: var(--color-base-content);
    background-color: var(--alert-color, var(--color-base-200));
    justify-content: start;
    justify-items: start;
    grid-auto-flow: column;
    grid-template-columns: auto;
    text-align: start;
    border: var(--border) solid var(--color-base-200);
    font-size: 0.875rem;
    line-height: 1.25rem;
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    box-shadow: 0 3px 0 -2px oklch(100% 0 0 / calc(var(--depth) * 0.08)) inset, 0 1px #000, 0 4px 3px -2px oklch(0% 0 0 / calc(var(--depth) * 0.08));
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 3px 0 -2px oklch(100% 0 0 / calc(var(--depth) * 0.08)) inset, 0 1px color-mix( in oklab, color-mix(in oklab, #000 20%, var(--alert-color, var(--color-base-200))) calc(var(--depth) * 20%), #0000 ), 0 4px 3px -2px oklch(0% 0 0 / calc(var(--depth) * 0.08));
    }
    &:has(:nth-child(2)) {
      grid-template-columns: auto minmax(auto, 1fr);
    }
    &.alert-outline {
      background-color: transparent;
      color: var(--alert-color);
      box-shadow: none;
      background-image: none;
    }
    &.alert-dash {
      background-color: transparent;
      color: var(--alert-color);
      border-style: dashed;
      box-shadow: none;
      background-image: none;
    }
    &.alert-soft {
      color: var(--alert-color, var(--color-base-content));
      background: var(--alert-color, var(--color-base-content));
      @supports (color: color-mix(in lab, red, red)) {
        background: color-mix( in oklab, var(--alert-color, var(--color-base-content)) 8%, var(--color-base-100) );
      }
      border-color: var(--alert-color, var(--color-base-content));
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix( in oklab, var(--alert-color, var(--color-base-content)) 10%, var(--color-base-100) );
      }
      box-shadow: none;
      background-image: none;
    }
  }
  .card-actions {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: calc(0.25rem * 2);
  }
  .card-title {
    display: flex;
    align-items: center;
    gap: calc(0.25rem * 2);
    font-size: var(--cardtitle-fs, 1.125rem);
    font-weight: 600;
  }
  .join {
    display: inline-flex;
    align-items: stretch;
    --join-ss: 0;
    --join-se: 0;
    --join-es: 0;
    --join-ee: 0;
    :where(.join-item) {
      border-start-start-radius: var(--join-ss, 0);
      border-start-end-radius: var(--join-se, 0);
      border-end-start-radius: var(--join-es, 0);
      border-end-end-radius: var(--join-ee, 0);
      * {
        --join-ss: var(--radius-field);
        --join-se: var(--radius-field);
        --join-es: var(--radius-field);
        --join-ee: var(--radius-field);
      }
    }
    > .join-item:where(:first-child) {
      --join-ss: var(--radius-field);
      --join-se: 0;
      --join-es: var(--radius-field);
      --join-ee: 0;
    }
    :first-child:not(:last-child) {
      :where(.join-item) {
        --join-ss: var(--radius-field);
        --join-se: 0;
        --join-es: var(--radius-field);
        --join-ee: 0;
      }
    }
    > .join-item:where(:last-child) {
      --join-ss: 0;
      --join-se: var(--radius-field);
      --join-es: 0;
      --join-ee: var(--radius-field);
    }
    :last-child:not(:first-child) {
      :where(.join-item) {
        --join-ss: 0;
        --join-se: var(--radius-field);
        --join-es: 0;
        --join-ee: var(--radius-field);
      }
    }
    > .join-item:where(:only-child) {
      --join-ss: var(--radius-field);
      --join-se: var(--radius-field);
      --join-es: var(--radius-field);
      --join-ee: var(--radius-field);
    }
    :only-child {
      :where(.join-item) {
        --join-ss: var(--radius-field);
        --join-se: var(--radius-field);
        --join-es: var(--radius-field);
        --join-ee: var(--radius-field);
      }
    }
  }
  .chat {
    display: grid;
    column-gap: calc(0.25rem * 3);
    padding-block: calc(0.25rem * 1);
    --mask-chat: url("data:image/svg+xml,%3csvg width='13' height='13' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M0 11.5004C0 13.0004 2 13.0004 2 13.0004H12H13V0.00036329L12.5 0C12.5 0 11.977 2.09572 11.8581 2.50033C11.6075 3.35237 10.9149 4.22374 9 5.50036C6 7.50036 0 10.0004 0 11.5004Z'/%3e%3c/svg%3e");
  }
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  .prose {
    :root & {
      --tw-prose-body: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-body: color-mix(in oklab, var(--color-base-content) 80%, #0000);
      }
      --tw-prose-headings: var(--color-base-content);
      --tw-prose-lead: var(--color-base-content);
      --tw-prose-links: var(--color-base-content);
      --tw-prose-bold: var(--color-base-content);
      --tw-prose-counters: var(--color-base-content);
      --tw-prose-bullets: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-bullets: color-mix(in oklab, var(--color-base-content) 50%, #0000);
      }
      --tw-prose-hr: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-hr: color-mix(in oklab, var(--color-base-content) 20%, #0000);
      }
      --tw-prose-quotes: var(--color-base-content);
      --tw-prose-quote-borders: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-quote-borders: color-mix(in oklab, var(--color-base-content) 20%, #0000);
      }
      --tw-prose-captions: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-captions: color-mix(in oklab, var(--color-base-content) 50%, #0000);
      }
      --tw-prose-code: var(--color-base-content);
      --tw-prose-pre-code: var(--color-neutral-content);
      --tw-prose-pre-bg: var(--color-neutral);
      --tw-prose-th-borders: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-th-borders: color-mix(in oklab, var(--color-base-content) 50%, #0000);
      }
      --tw-prose-td-borders: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-td-borders: color-mix(in oklab, var(--color-base-content) 20%, #0000);
      }
      --tw-prose-kbd: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-prose-kbd: color-mix(in oklab, var(--color-base-content) 80%, #0000);
      }
      :where(code):not(pre > code) {
        background-color: var(--color-base-200);
        border-radius: var(--radius-selector);
        border: var(--border) solid var(--color-base-300);
        padding-inline: 0.5em;
        font-weight: inherit;
        &:before, &:after {
          display: none;
        }
      }
    }
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .table {
    display: table;
  }
  .btn-circle {
    border-radius: calc(infinity * 1px);
    padding-inline: calc(0.25rem * 0);
    width: var(--size);
    height: var(--size);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-20 {
    height: calc(var(--spacing) * 20);
  }
  .h-32 {
    height: calc(var(--spacing) * 32);
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }
  .max-h-screen {
    max-height: 100vh;
  }
  .min-h-\[48px\] {
    min-height: 48px;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .btn-block {
    width: 100%;
  }
  .loading-lg {
    width: calc(var(--size-selector, 0.25rem) * 7);
  }
  .loading-md {
    width: calc(var(--size-selector, 0.25rem) * 6);
  }
  .loading-sm {
    width: calc(var(--size-selector, 0.25rem) * 5);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-11 {
    width: calc(var(--spacing) * 11);
  }
  .w-11\/12 {
    width: calc(11/12 * 100%);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-56 {
    width: calc(var(--spacing) * 56);
  }
  .w-auto {
    width: auto;
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-6xl {
    max-width: var(--container-6xl);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-none {
    max-width: none;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-none {
    flex: none;
  }
  .flex-shrink {
    flex-shrink: 1;
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .skeleton {
    border-radius: var(--radius-box);
    background-color: var(--color-base-300);
    @media (prefers-reduced-motion: reduce) {
      transition-duration: 15s;
    }
    will-change: background-position;
    animation: skeleton 1.8s ease-in-out infinite;
    background-image: linear-gradient( 105deg, #0000 0% 40%, var(--color-base-100) 50%, #0000 60% 100% );
    background-size: 200% auto;
    background-repeat: no-repeat;
    background-position-x: -50%;
  }
  .link {
    cursor: pointer;
    text-decoration-line: underline;
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    &:focus-visible {
      outline: 2px solid currentColor;
      outline-offset: 2px;
    }
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-disc {
    list-style-type: disc;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .menu-sm {
    :where(li:not(.menu-title) > *:not(ul, details, .menu-title)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 2.5);
      padding-block: calc(0.25rem * 1);
      font-size: 0.75rem;
    }
    .menu-title {
      padding-inline: calc(0.25rem * 3);
      padding-block: calc(0.25rem * 2);
    }
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-box {
    border-radius: var(--radius-box);
  }
  .rounded-box {
    border-radius: var(--radius-box);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-1 {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }
  .badge-ghost {
    border-color: var(--color-base-200);
    background-color: var(--color-base-200);
    color: var(--color-base-content);
    background-image: none;
  }
  .badge-outline {
    color: var(--badge-color);
    --badge-bg: #0000;
    background-image: none;
    border-color: currentColor;
  }
  .alert-error {
    border-color: var(--color-error);
    color: var(--color-error-content);
    --alert-color: var(--color-error);
  }
  .alert-info {
    border-color: var(--color-info);
    color: var(--color-info-content);
    --alert-color: var(--color-info);
  }
  .alert-success {
    border-color: var(--color-success);
    color: var(--color-success-content);
    --alert-color: var(--color-success);
  }
  .alert-warning {
    border-color: var(--color-warning);
    color: var(--color-warning-content);
    --alert-color: var(--color-warning);
  }
  .border-base-300 {
    border-color: var(--color-base-300);
  }
  .border-base-content {
    border-color: var(--color-base-content);
  }
  .border-base-content\/40 {
    border-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }
  .border-black {
    border-color: var(--color-black);
  }
  .border-info {
    border-color: var(--color-info);
  }
  .border-info\/20 {
    border-color: var(--color-info);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-info) 20%, transparent);
    }
  }
  .border-neutral-content {
    border-color: var(--color-neutral-content);
  }
  .border-primary {
    border-color: var(--color-primary);
  }
  .border-success {
    border-color: var(--color-success);
  }
  .border-success\/20 {
    border-color: var(--color-success);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-success) 20%, transparent);
    }
  }
  .border-warning {
    border-color: var(--color-warning);
  }
  .border-warning\/20 {
    border-color: var(--color-warning);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-warning) 20%, transparent);
    }
  }
  .table-zebra {
    tbody {
      tr {
        &:where(:nth-child(even)) {
          background-color: var(--color-base-200);
          :where(.table-pin-cols tr th) {
            background-color: var(--color-base-200);
          }
        }
        &.row-hover {
          &, &:where(:nth-child(even)) {
            &:hover {
              @media (hover: hover) {
                background-color: var(--color-base-300);
              }
            }
          }
        }
      }
    }
  }
  .bg-accent {
    background-color: var(--color-accent);
  }
  .bg-accent\/20 {
    background-color: var(--color-accent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-accent) 20%, transparent);
    }
  }
  .bg-base-100 {
    background-color: var(--color-base-100);
  }
  .bg-base-200 {
    background-color: var(--color-base-200);
  }
  .bg-base-300 {
    background-color: var(--color-base-300);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-info {
    background-color: var(--color-info);
  }
  .bg-info\/10 {
    background-color: var(--color-info);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-info) 10%, transparent);
    }
  }
  .bg-info\/20 {
    background-color: var(--color-info);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-info) 20%, transparent);
    }
  }
  .bg-neutral {
    background-color: var(--color-neutral);
  }
  .bg-primary {
    background-color: var(--color-primary);
  }
  .bg-primary\/10 {
    background-color: var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-primary) 10%, transparent);
    }
  }
  .bg-primary\/20 {
    background-color: var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-primary) 20%, transparent);
    }
  }
  .bg-success {
    background-color: var(--color-success);
  }
  .bg-success\/10 {
    background-color: var(--color-success);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-success) 10%, transparent);
    }
  }
  .bg-success\/20 {
    background-color: var(--color-success);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-success) 20%, transparent);
    }
  }
  .bg-warning {
    background-color: var(--color-warning);
  }
  .bg-warning\/10 {
    background-color: var(--color-warning);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-warning) 10%, transparent);
    }
  }
  .bg-warning\/20 {
    background-color: var(--color-warning);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-warning) 20%, transparent);
    }
  }
  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-info {
    --tw-gradient-from: var(--color-info);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-info\/10 {
    --tw-gradient-from: var(--color-info);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-from: color-mix(in oklab, var(--color-info) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-primary {
    --tw-gradient-from: var(--color-primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-primary {
    --tw-gradient-to: var(--color-primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-primary\/10 {
    --tw-gradient-to: var(--color-primary);
    @supports (color: color-mix(in lab, red, red)) {
      --tw-gradient-to: color-mix(in oklab, var(--color-primary) 10%, transparent);
    }
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-secondary {
    --tw-gradient-to: var(--color-secondary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .loading-spinner {
    mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
  }
  .mask-repeat {
    mask-repeat: repeat;
  }
  .stroke-current {
    stroke: currentcolor;
  }
  .radio-sm {
    padding: 0.1875rem;
    &:is([type="radio"]) {
      --size: calc(var(--size-selector, 0.25rem) * 5);
    }
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .menu-title {
    padding-inline: calc(0.25rem * 3);
    padding-block: calc(0.25rem * 2);
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
    font-size: 0.875rem;
    font-weight: 600;
  }
  .table-xs {
    :not(thead, tfoot) tr {
      font-size: 0.6875rem;
    }
    :where(th, td) {
      padding-inline: calc(0.25rem * 2);
      padding-block: calc(0.25rem * 1);
    }
  }
  .badge-lg {
    --size: calc(var(--size-selector, 0.25rem) * 7);
    font-size: 1rem;
    padding-inline: calc(0.25rem * 3.5 - var(--border));
  }
  .badge-sm {
    --size: calc(var(--size-selector, 0.25rem) * 5);
    font-size: 0.75rem;
    padding-inline: calc(0.25rem * 2.5 - var(--border));
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }
  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .align-top {
    vertical-align: top;
  }
  .font-mono {
    font-family: var(--font-mono);
  }
  .font-poppins {
    font-family: var(--font-poppins);
  }
  .font-raleway {
    font-family: var(--font-raleway);
  }
  .font-roboto {
    font-family: var(--font-roboto);
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-5 {
    --tw-leading: calc(var(--spacing) * 5);
    line-height: calc(var(--spacing) * 5);
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .text-wrap {
    text-wrap: wrap;
  }
  .break-all {
    word-break: break-all;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }
  .checkbox-primary {
    color: var(--color-primary-content);
    --input-color: var(--color-primary);
  }
  .progress-primary {
    color: var(--color-primary);
  }
  .text-accent {
    color: var(--color-accent);
  }
  .text-accent-content {
    color: var(--color-accent-content);
  }
  .text-base-content {
    color: var(--color-base-content);
  }
  .text-base-content\/20 {
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
    }
  }
  .text-base-content\/30 {
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 30%, transparent);
    }
  }
  .text-base-content\/40 {
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
    }
  }
  .text-base-content\/50 {
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 50%, transparent);
    }
  }
  .text-base-content\/60 {
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
    }
  }
  .text-base-content\/70 {
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 70%, transparent);
    }
  }
  .text-base-content\/80 {
    color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, var(--color-base-content) 80%, transparent);
    }
  }
  .text-black {
    color: var(--color-black);
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-error {
    color: var(--color-error);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-info {
    color: var(--color-info);
  }
  .text-neutral-content {
    color: var(--color-neutral-content);
  }
  .text-orange-500 {
    color: var(--color-orange-500);
  }
  .text-primary {
    color: var(--color-primary);
  }
  .text-primary-content {
    color: var(--color-primary-content);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-secondary {
    color: var(--color-secondary);
  }
  .text-secondary-content {
    color: var(--color-secondary-content);
  }
  .text-success {
    color: var(--color-success);
  }
  .text-success-content {
    color: var(--color-success-content);
  }
  .text-warning {
    color: var(--color-warning);
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow-500 {
    color: var(--color-yellow-500);
  }
  .capitalize {
    text-transform: capitalize;
  }
  .italic {
    font-style: italic;
  }
  .underline {
    text-decoration-line: underline;
  }
  .opacity-70 {
    opacity: 70%;
  }
  .opacity-75 {
    opacity: 75%;
  }
  .opacity-90 {
    opacity: 90%;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .btn-ghost {
    &:not(.btn-active, :hover, :active:focus, :focus-visible) {
      --btn-shadow: "";
      --btn-bg: #0000;
      --btn-border: #0000;
      --btn-noise: none;
      &:not(:disabled, [disabled], .btn-disabled) {
        outline-color: currentColor;
        --btn-fg: currentColor;
      }
    }
    @media (hover: none) {
      &:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled) {
        --btn-shadow: "";
        --btn-bg: #0000;
        --btn-border: #0000;
        --btn-noise: none;
        --btn-fg: currentColor;
      }
    }
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .btn-outline {
    &:not( .btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
      --btn-shadow: "";
      --btn-bg: #0000;
      --btn-fg: var(--btn-color);
      --btn-border: var(--btn-color);
      --btn-noise: none;
    }
    @media (hover: none) {
      &:hover:not( .btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
        --btn-shadow: "";
        --btn-bg: #0000;
        --btn-fg: var(--btn-color);
        --btn-border: var(--btn-color);
        --btn-noise: none;
      }
    }
  }
  .btn-lg {
    --fontsize: 1.125rem;
    --btn-p: 1.25rem;
    --size: calc(var(--size-field, 0.25rem) * 12);
  }
  .btn-sm {
    --fontsize: 0.75rem;
    --btn-p: 0.75rem;
    --size: calc(var(--size-field, 0.25rem) * 8);
  }
  .btn-xs {
    --fontsize: 0.6875rem;
    --btn-p: 0.5rem;
    --size: calc(var(--size-field, 0.25rem) * 6);
  }
  .badge-accent {
    --badge-color: var(--color-accent);
    --badge-fg: var(--color-accent-content);
  }
  .badge-error {
    --badge-color: var(--color-error);
    --badge-fg: var(--color-error-content);
  }
  .badge-info {
    --badge-color: var(--color-info);
    --badge-fg: var(--color-info-content);
  }
  .badge-neutral {
    --badge-color: var(--color-neutral);
    --badge-fg: var(--color-neutral-content);
  }
  .badge-primary {
    --badge-color: var(--color-primary);
    --badge-fg: var(--color-primary-content);
  }
  .badge-secondary {
    --badge-color: var(--color-secondary);
    --badge-fg: var(--color-secondary-content);
  }
  .badge-success {
    --badge-color: var(--color-success);
    --badge-fg: var(--color-success-content);
  }
  .badge-warning {
    --badge-color: var(--color-warning);
    --badge-fg: var(--color-warning-content);
  }
  .btn-error {
    --btn-color: var(--color-error);
    --btn-fg: var(--color-error-content);
  }
  .btn-primary {
    --btn-color: var(--color-primary);
    --btn-fg: var(--color-primary-content);
  }
  .btn-secondary {
    --btn-color: var(--color-secondary);
    --btn-fg: var(--color-secondary-content);
  }
  .btn-success {
    --btn-color: var(--color-success);
    --btn-fg: var(--color-success-content);
  }
  .btn-warning {
    --btn-color: var(--color-warning);
    --btn-fg: var(--color-warning-content);
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .input-error {
    &, &:focus, &:focus-within {
      --input-color: var(--color-error);
    }
  }
  .radio-primary {
    --input-color: var(--color-primary);
  }
  .toggle-primary {
    &:checked, &[aria-checked="true"] {
      --input-color: var(--color-primary);
    }
  }
  .toggle-sm {
    &:is([type="checkbox"]), &:has([type="checkbox"]) {
      --size: calc(var(--size-selector, 0.25rem) * 5);
    }
  }
  .group-hover\:text-red-700 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-red-700);
      }
    }
  }
  .checked\:bg-blue-500 {
    &:checked {
      background-color: var(--color-blue-500);
    }
  }
  .hover\:border-primary {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-primary);
      }
    }
  }
  .hover\:bg-base-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-base-200);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-red-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-100);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .hover\:shadow-md {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .focus\:border-neutral-content {
    &:focus {
      border-color: var(--color-neutral-content);
    }
  }
  .focus\:ring-0 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .md\:col-span-1 {
    @media (width >= 48rem) {
      grid-column: span 1 / span 1;
    }
  }
  .md\:col-span-2 {
    @media (width >= 48rem) {
      grid-column: span 2 / span 2;
    }
  }
  .md\:mx-16 {
    @media (width >= 48rem) {
      margin-inline: calc(var(--spacing) * 16);
    }
  }
  .md\:mt-0 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .md\:mt-8 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 8);
    }
  }
  .md\:mb-6 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }
  .md\:mb-16 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 16);
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:w-12 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 12);
    }
  }
  .md\:w-16 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 16);
    }
  }
  .md\:max-w-\[320px\] {
    @media (width >= 48rem) {
      max-width: 320px;
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-10 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(10, minmax(0, 1fr));
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:items-center {
    @media (width >= 48rem) {
      align-items: center;
    }
  }
  .md\:space-y-6 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .md\:p-6 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .md\:px-16 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 16);
    }
  }
  .md\:py-4 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 4);
    }
  }
  .md\:text-3xl {
    @media (width >= 48rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .md\:text-base {
    @media (width >= 48rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .md\:text-lg {
    @media (width >= 48rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .md\:text-sm {
    @media (width >= 48rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .md\:text-xl {
    @media (width >= 48rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .lg\:col-span-1 {
    @media (width >= 64rem) {
      grid-column: span 1 / span 1;
    }
  }
  .lg\:col-span-2 {
    @media (width >= 64rem) {
      grid-column: span 2 / span 2;
    }
  }
  .lg\:max-w-\[480px\] {
    @media (width >= 64rem) {
      max-width: 480px;
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-10 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(10, minmax(0, 1fr));
    }
  }
  .xl\:grid-cols-5 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }
}
.my-h1 {
  margin-bottom: calc(var(--spacing) * 2);
  font-family: var(--font-raleway);
  font-size: var(--text-lg);
  line-height: var(--tw-leading, var(--text-lg--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  @media (width >= 48rem) {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
}
.my-h2 {
  font-family: var(--font-roboto);
  font-size: var(--text-base);
  line-height: var(--tw-leading, var(--text-base--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  @media (width >= 48rem) {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
}
.my-h3 {
  font-family: var(--font-roboto);
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  @media (width >= 48rem) {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
}
.my-p {
  font-family: var(--font-poppins);
  font-size: var(--text-xs);
  line-height: var(--tw-leading, var(--text-xs--line-height));
  @media (width >= 48rem) {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
}
.my-input {
  width: 100%;
  border-radius: var(--radius-sm);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-accent-content);
  padding: calc(var(--spacing) * 6);
  padding-right: calc(var(--spacing) * 10);
  --tw-outline-style: none;
  outline-style: none;
  &:focus {
    border-color: var(--color-accent-content);
  }
  &:focus {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  &:focus {
    --tw-outline-style: none;
    outline-style: none;
  }
}
.my-card {
  :where(& > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }
  border-radius: var(--radius-sm);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-base-300);
  background-color: var(--color-base-100);
  padding: calc(var(--spacing) * 4);
  --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  @media (width >= 48rem) {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  @media (width >= 48rem) {
    padding: calc(var(--spacing) * 6);
  }
}
@layer base {
  :where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  @media (prefers-color-scheme: dark) {
    :root {
      color-scheme: dark;
      --color-base-100: oklch(25.33% 0.016 252.42);
      --color-base-200: oklch(23.26% 0.014 253.1);
      --color-base-300: oklch(21.15% 0.012 254.09);
      --color-base-content: oklch(97.807% 0.029 256.847);
      --color-primary: oklch(58% 0.233 277.117);
      --color-primary-content: oklch(96% 0.018 272.314);
      --color-secondary: oklch(65% 0.241 354.308);
      --color-secondary-content: oklch(94% 0.028 342.258);
      --color-accent: oklch(77% 0.152 181.912);
      --color-accent-content: oklch(38% 0.063 188.416);
      --color-neutral: oklch(14% 0.005 285.823);
      --color-neutral-content: oklch(92% 0.004 286.32);
      --color-info: oklch(74% 0.16 232.661);
      --color-info-content: oklch(29% 0.066 243.157);
      --color-success: oklch(76% 0.177 163.223);
      --color-success-content: oklch(37% 0.077 168.94);
      --color-warning: oklch(82% 0.189 84.429);
      --color-warning-content: oklch(41% 0.112 45.904);
      --color-error: oklch(71% 0.194 13.428);
      --color-error-content: oklch(27% 0.105 12.094);
      --radius-selector: 0.5rem;
      --radius-field: 0.25rem;
      --radius-box: 0.5rem;
      --size-selector: 0.25rem;
      --size-field: 0.25rem;
      --border: 1px;
      --depth: 1;
      --noise: 0;
    }
  }
}
@layer base {
  :root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :root:has(input.theme-controller[value=dark]:checked),[data-theme=dark] {
    color-scheme: dark;
    --color-base-100: oklch(25.33% 0.016 252.42);
    --color-base-200: oklch(23.26% 0.014 253.1);
    --color-base-300: oklch(21.15% 0.012 254.09);
    --color-base-content: oklch(97.807% 0.029 256.847);
    --color-primary: oklch(58% 0.233 277.117);
    --color-primary-content: oklch(96% 0.018 272.314);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :root {
    --fx-noise: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E");
  }
}
@layer base {
  :root, [data-theme] {
    background-color: var(--root-bg, var(--color-base-100));
    color: var(--color-base-content);
  }
}
@layer base {
  :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not([class*="drawer-open"]) > .drawer-toggle:checked ) {
    overflow: hidden;
  }
}
@layer base {
  @property --radialprogress {
    syntax: "<percentage>";
    inherits: true;
    initial-value: 0%;
  }
}
@layer base {
  :where( :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not(.drawer-open) > .drawer-toggle:checked ) ) {
    scrollbar-gutter: stable;
    background-image: linear-gradient(var(--color-base-100), var(--color-base-100));
    --root-bg: var(--color-base-100);
    @supports (color: color-mix(in lab, red, red)) {
      --root-bg: color-mix(in srgb, var(--color-base-100), oklch(0% 0 0) 40%);
    }
  }
  :where(.modal[open], .modal-open, .modal-toggle:checked + .modal):not(.modal-start, .modal-end) {
    scrollbar-gutter: stable;
  }
}
@layer base {
  :root {
    scrollbar-color: currentColor #0000;
    @supports (color: color-mix(in lab, red, red)) {
      scrollbar-color: color-mix(in oklch, currentColor 35%, #0000) #0000;
    }
  }
}
@keyframes progress {
  50% {
    background-position-x: -115%;
  }
}
@keyframes rating {
  0%, 40% {
    scale: 1.1;
    filter: brightness(1.05) contrast(1.05);
  }
}
@keyframes skeleton {
  0% {
    background-position: 150%;
  }
  100% {
    background-position: -50%;
  }
}
@keyframes dropdown {
  0% {
    opacity: 0;
  }
}
@keyframes radio {
  0% {
    padding: 5px;
  }
  50% {
    padding: 3px;
  }
}
@keyframes toast {
  0% {
    scale: 0.9;
    opacity: 0;
  }
  100% {
    scale: 1;
    opacity: 1;
  }
}
@layer base {
  :where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme="light"] {
    color-scheme: light;
    --color-base-100: oklch(98% 0.016 73.684);
    --color-base-200: oklch(95% 0.038 75.164);
    --color-base-300: oklch(90% 0.076 70.697);
    --color-base-content: oklch(40% 0.123 38.172);
    --color-primary: oklch(0% 0 0);
    --color-primary-content: oklch(100% 0 0);
    --color-secondary: oklch(22.45% 0.075 37.85);
    --color-secondary-content: oklch(90% 0.076 70.697);
    --color-accent: oklch(46.44% 0.111 37.85);
    --color-accent-content: oklch(90% 0.076 70.697);
    --color-neutral: oklch(55% 0.195 38.402);
    --color-neutral-content: oklch(98% 0.016 73.684);
    --color-info: oklch(42% 0.199 265.638);
    --color-info-content: oklch(90% 0.076 70.697);
    --color-success: oklch(0.609 0.105 184.023);
    --color-success-content: oklch(90% 0.076 70.697);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(70% 0.191 22.216);
    --color-error-content: oklch(39% 0.141 25.723);
    --radius-selector: 0.25rem;
    --radius-field: 0.25rem;
    --radius-box: 0.25rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :root:has(input.theme-controller[value=dark]:checked),[data-theme="dark"] {
    color-scheme: dark;
    --color-base-100: oklch(20.768% 0.039 265.754);
    --color-base-200: oklch(19.314% 0.037 265.754);
    --color-base-300: oklch(17.86% 0.034 265.754);
    --color-base-content: oklch(84.153% 0.007 265.754);
    --color-primary: oklch(75.351% 0.138 232.661);
    --color-primary-content: oklch(15.07% 0.027 232.661);
    --color-secondary: oklch(68.011% 0.158 276.934);
    --color-secondary-content: oklch(13.602% 0.031 276.934);
    --color-accent: oklch(72.36% 0.176 350.048);
    --color-accent-content: oklch(14.472% 0.035 350.048);
    --color-neutral: oklch(27.949% 0.036 260.03);
    --color-neutral-content: oklch(85.589% 0.007 260.03);
    --color-info: oklch(68.455% 0.148 237.251);
    --color-info-content: oklch(0% 0 0);
    --color-success: oklch(78.452% 0.132 181.911);
    --color-success-content: oklch(15.69% 0.026 181.911);
    --color-warning: oklch(83.242% 0.139 82.95);
    --color-warning-content: oklch(16.648% 0.027 82.95);
    --color-error: oklch(71.785% 0.17 13.118);
    --color-error-content: oklch(14.357% 0.034 13.118);
    --radius-selector: 0.25rem;
    --radius-field: 0.25rem;
    --radius-box: 0.25rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
    }
  }
}
