from django.urls import path
from ..all_views.accounts_views import (
    profile,
    CustomLoginView,
    CustomLogoutView,
    CustomSignupView,
    CustomPasswordResetView,
    CustomPasswordChangeView,
    CustomConfirmEmailView,
    CustomEmailVerificationSentView,
    CustomEmailView,
    CustomPasswordResetDoneView,
    CustomPasswordResetFromKeyView,
    UserNameChangeView,
    delete_account,
)

app_name = "account"

urlpatterns = [
    path("profile/", profile, name="profile"),
    path("login/", CustomLoginView.as_view(), name="login"),
    path("logout/", CustomLogoutView.as_view(), name="logout"),
    path("signup/", CustomSignupView.as_view(), name="signup"),
    path("password/change/", CustomPasswordChangeView.as_view(), name="password_change"),
    path("password/reset/", CustomPasswordResetView.as_view(), name="password_reset"),
    path(
        "password/reset/done/",
        CustomPasswordResetDoneView.as_view(),
        name="password_reset_done",
    ),
    path(
        "password/reset/key/<uidb36>/<key>/",
        CustomPasswordResetFromKeyView.as_view(),
        name="password_reset_from_key",
    ),
    path(
        "confirm-email/",
        CustomEmailVerificationSentView.as_view(),
        name="account_email_verification_sent",
    ),
    path(
        "confirm-email/<key>/",
        CustomConfirmEmailView.as_view(),
        name="account_confirm_email",
    ),
    path("email/", CustomEmailView.as_view(), name="account_email"),
    path("name-change/", UserNameChangeView.as_view(), name="name_change"),
    path("delete/", delete_account, name="delete"),
]