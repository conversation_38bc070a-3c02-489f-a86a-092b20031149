from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.views.generic.edit import UpdateView

from allauth.account.views import (
    ConfirmEmailView,
    EmailVerificationSentView,
    EmailView,
    LogoutView,
    LoginView,
    PasswordChangeView,
    PasswordResetView,
    PasswordResetDoneView,
    PasswordResetFromKeyView,
    SignupView,
)


@login_required
def profile(request):
    """User profile view."""
    request.session["current_view"] = "account:profile"
    return render(request, "account/profile.html")


class CustomLogoutView(LogoutView):
    template_name = "account/logout.html"


class CustomLoginView(LoginView):
    template_name = "account/login.html"


class CustomSignupView(SignupView):
    template_name = "account/signup.html"


class CustomPasswordResetView(PasswordResetView):
    template_name = "account/password_reset.html"


class CustomPasswordChangeView(PasswordChangeView):
    template_name = "account/password_change.html"


class CustomConfirmEmailView(ConfirmEmailView):
    template_name = "account/confirm_email.html"


class CustomEmailVerificationSentView(EmailVerificationSentView):
    template_name = "account/verification_sent.html"


class CustomEmailView(EmailView):
    template_name = "account/email.html"


class CustomPasswordResetDoneView(PasswordResetDoneView):
    template_name = "account/password_reset_done.html"


class CustomPasswordResetFromKeyView(PasswordResetFromKeyView):
    template_name = "account/password_reset_from_key.html"
    reset_url_token = "set-password"


class UserNameChangeView(LoginRequiredMixin, UpdateView):
    """View for updating user's first name and last name."""
    template_name = "account/name_change.html"
    success_url = reverse_lazy("account:profile")
    fields = ['first_name', 'last_name']

    def get_object(self, queryset=None):
        return self.request.user


@login_required
def delete_account(request):
    """View for deleting a user account."""
    if request.method == "POST":
        user = request.user
        user.delete()
        return redirect("home")
    return redirect("account:profile")
